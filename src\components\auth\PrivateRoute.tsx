import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useRequireAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from './LoadingSpinner';

interface PrivateRouteProps {
  children: React.ReactNode;
}

export const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { isLoading, shouldRedirect } = useRequireAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (shouldRedirect) {
    // Redirect to loginff page with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};
