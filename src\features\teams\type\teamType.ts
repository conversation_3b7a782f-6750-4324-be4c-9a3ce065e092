export interface SocialMedia {
  website?: string;
  facebook?: string;
  linkedin?: string;
  instagram?: string;
  github?: string;
}

export interface TeamType {
  _id: string; // MongoDB ObjectId (primary identifier)
  id?: number; // Auto-increment ID for compatibility
  fixedId: string; // Fixed ID for frontend routing
  name: string;
  sortOrder: number;
  image: string;
  imageUrl?: string; // Full URL for display
  jobCategory?: string; // Job category/role
  status?: 'active' | 'inactive'; // Status of team member
  createdAt: string;
  updatedAt: string;
}

// Job categories enum matching backend schema
export const JOB_CATEGORIES = [
  "Frontend Developer",
  "Backend Developer",
  "Full Stack Developer",
  "UI/UX Designer",
  "Project Manager",
  "DevOps Engineer",
  "Data Scientist",
  "Mobile App Developer",
  "Digital Marketing Specialist",
  "SEO Specialist",
  "Content Writer",
  "Business Analyst",
  "Quality Assurance",
  "Team Lead",
  "CTO",
  "CEO",
  "Founder",
  "Co-Founder",
  "Technical Architect",
  "Product Manager"
] as const;

export type JobCategory = typeof JOB_CATEGORIES[number];
