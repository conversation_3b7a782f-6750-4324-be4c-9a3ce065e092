import React from 'react';
import { Routes, Route } from 'react-router-dom';
import VenueBookingsList from '../components/VenueBookingsList';
import VenueBookingDetail from '../components/VenueBookingDetail';

const VenueBookingsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<VenueBookingsList />} />
      <Route path="upcoming" element={<div className="p-6"><h1 className="text-2xl font-bold">Upcoming Bookings</h1><p className="text-gray-600 mt-2">Feature coming soon...</p></div>} />
      <Route path=":id" element={<VenueBookingDetail />} />
    </Routes>
  );
};

export default VenueBookingsRoutes;
