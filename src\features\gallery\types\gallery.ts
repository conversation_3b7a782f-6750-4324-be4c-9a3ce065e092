export interface Gallery {
  _id: string;
  fixedId: string;
  title: string;
  description: string;
  keywords: string[];
  category: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  sortOrder: number;
  image: string;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateGalleryRequest {
  title: string;
  description: string;
  keywords: string[];
  category: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  sortOrder: number;
  image: File;
}

export interface UpdateGalleryRequest {
  title?: string;
  description?: string;
  keywords?: string[];
  category?: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  sortOrder?: number;
  image?: File;
}

export interface GalleryFilters {
  category?: string;
  keyword?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const GALLERY_CATEGORIES = [
  { value: 'wedding', label: 'Wedding' },
  { value: 'corporate', label: 'Corporate' },
  { value: 'birthday', label: 'Birthday' },
  { value: 'anniversary', label: 'Anniversary' },
  { value: 'other', label: 'Other' },
] as const;
