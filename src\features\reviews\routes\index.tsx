import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ReviewsList from '../components/ReviewsList';
import ReviewForm from '../components/ReviewForm';
import ReviewDetail from '../components/ReviewDetail';

const ReviewsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ReviewsList />} />
      <Route path="create" element={<ReviewForm mode="create" />} />
      <Route path="edit/:id" element={<ReviewForm mode="edit" />} />
      <Route path=":id" element={<ReviewDetail />} />
    </Routes>
  );
};

export default ReviewsRoutes;
