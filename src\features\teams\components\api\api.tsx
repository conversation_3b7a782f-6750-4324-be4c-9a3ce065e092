import { teamsEndpoints } from "@/globalurl/baseurl";
import axios from "axios";
import { TeamType } from "../../type/teamType";

// API Response Types
interface ApiResponse<T = any> {
  success?: boolean;
  status?: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

interface PaginatedResponse<T> {
  teamMembers?: T[];
  data?: T[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  totalPages?: number;
  currentPage?: number;
  totalItems?: number;
}

// Request Types (matching new backend API structure)
interface CreateTeamRequest {
  name: string;
  sortOrder?: number;
  image?: File | null; // Will be uploaded and stored as imageUrl
}

interface UpdateTeamRequest {
  id: string; // Use fixedId or _id
  name?: string;
  sortOrder?: number;
  image?: File | null;
}

interface GetTeamsRequest {
  page: number;
  pageSize: number;
  keyword?: string;
}

// ===== CREATE TEAM =====
export const createTeam = async ({
  name,
  sortOrder = 1,
  image
}: CreateTeamRequest): Promise<ApiResponse<TeamType>> => {
  try {
    const formData = new FormData();

    formData.append("name", name);
    formData.append("sortOrder", sortOrder.toString());

    if (image) {
      formData.append("image", image);
    }

    const response = await axios.post(teamsEndpoints.create, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      status: true,
      success: true,
      data: response.data.data || response.data,
      message: "Team member created successfully"
    };
  } catch (error: any) {
    console.error("Create team error:", error);
    return {
      status: false,
      success: false,
      error: error.response?.data?.message || error.message || "Failed to create team member"
    };
  }
};

// ===== GET ALL TEAMS =====
export const getAllTeams = async ({
  page,
  pageSize,
  keyword = "",
}: GetTeamsRequest): Promise<ApiResponse<PaginatedResponse<TeamType>>> => {
  try {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('limit', pageSize.toString());
    if (keyword) params.append('search', keyword);

    const url = params.toString() ? `${teamsEndpoints.getAll}?${params}` : teamsEndpoints.getAll;
    const response = await axios.get(url);

    return {
      status: true,
      success: true,
      data: response.data.data || response.data,
      message: "Teams fetched successfully"
    };
  } catch (error: any) {
    console.error("Get teams error:", error);
    return {
      status: false,
      success: false,
      error: error.response?.data?.message || error.message || "Failed to fetch teams"
    };
  }
};

// ===== GET TEAM BY ID =====
export const getTeamById = async (id: string | number): Promise<ApiResponse<TeamType>> => {
  try {
    const response = await axios.get(teamsEndpoints.getById(id.toString()));

    return {
      status: true,
      success: true,
      data: response.data.data || response.data,
      message: "Team member fetched successfully"
    };
  } catch (error: any) {
    console.error("Get team by ID error:", error);
    return {
      status: false,
      success: false,
      error: error.response?.data?.message || error.message || "Failed to fetch team member"
    };
  }
};

// ===== UPDATE TEAM =====
export const updateTeam = async ({
  id,
  name,
  sortOrder,
  image
}: UpdateTeamRequest): Promise<ApiResponse<TeamType>> => {
  try {
    console.log("updateTeam API call with ID:", id);

    const formData = new FormData();
    if (name) formData.append("name", name);
    if (sortOrder !== undefined) formData.append("sortOrder", sortOrder.toString());

    if (image) {
      formData.append("image", image);
    }

    const response = await axios.put(teamsEndpoints.update(id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      status: true,
      success: true,
      data: response.data.data || response.data,
      message: "Team member updated successfully"
    };
  } catch (error: any) {
    console.error("Update team error:", error);
    console.error("Error response:", error.response?.data);
    console.error("Error status:", error.response?.status);
    return {
      status: false,
      success: false,
      error: error.response?.data?.message || error.message || "Failed to update team member"
    };
  }
};

// ===== DELETE TEAM =====
export const deleteTeam = async (id: string | number): Promise<ApiResponse> => {
  try {
    console.log("deleteTeam API call with ID:", id);

    const response = await axios.delete(teamsEndpoints.delete(id.toString()));

    return {
      status: true,
      success: true,
      data: response.data.data || response.data,
      message: "Team member deleted successfully"
    };
  } catch (error: any) {
    console.error("Delete team error:", error);
    console.error("Error response:", error.response?.data);
    console.error("Error status:", error.response?.status);
    return {
      status: false,
      success: false,
      error: error.response?.data?.message || error.message || "Failed to delete team member"
    };
  }
};

// ===== LEGACY SUPPORT FUNCTIONS =====
// These functions provide backward compatibility if needed
export const useGetTeamList = getAllTeams;
export const useCreateTeam = createTeam;
export const useUpdateTeam = updateTeam;
export const useDeleteTeam = deleteTeam;
