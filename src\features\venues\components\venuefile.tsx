// @ts-ignore
import React, { useState } from 'react';
import VenueTable from './venuetable';
import CreateVenueModal from './venuemodal';
import EditVenueModal from './venueeditmodal';
import { VenueType } from './venuetablecolumn';
import { toast } from 'react-toastify';

const initialData: VenueType[] = [
  { id: 1, name: 'Grand Hall', location: 'Mumbai', category: 'Conference', photo: 'grand_hall.jpg', totalCapacity: 500, totalSeats: 400 },
  { id: 2, name: 'Bliss Venue', location: 'Delhi', category: 'Wedding', photo: 'bliss_venue.jpg', totalCapacity: 300, totalSeats: 250 },
  { id: 3, name: 'Star Arena', location: 'Bangalore', category: 'Concert', photo: 'star_arena.jpg', totalCapacity: 1000, totalSeats: 800 },
  { id: 4, name: 'Party Plaza', location: 'Chennai', category: 'Party', photo: 'party_plaza.jpg', totalCapacity: 200, totalSeats: 150 },
];

const VenueFile = () => {
  const [venues] = useState<VenueType[]>(initialData);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Removed unused functions for now

  const openEditModal = (_venue: VenueType) => {
    // For demo purposes, just open modal
    setIsEditModalOpen(true);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
  };

  return (
    <div className="p-3 bg-[#F0EFF3] min-h-screen flex flex-col gap-3">
      <div className="flex items-center justify-between">
        <span className="text-lg font-bold text-[#313131]">Venues Data</span>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          Add Venue
        </button>
      </div>
      <VenueTable data={venues} openEditModal={openEditModal} />

      <CreateVenueModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={() => {
          setIsCreateModalOpen(false);
          // In real app, this would refresh data from API
          toast.success('Venue created successfully!');
        }}
      />

      <EditVenueModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          // In real app, this would refresh data from API
          toast.success('Venue updated successfully!');
        }}
        venue={null} // This component uses old VenueType, so passing null for now
      />
    </div>
  );
};

export default VenueFile;