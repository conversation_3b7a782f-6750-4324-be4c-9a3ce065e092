import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Search, Filter, Calendar, Phone, Mail } from 'lucide-react';
import { VenueBooking, VenueBookingFilters, BOOKING_STATUSES, BOOKING_PRIORITIES } from '../types/venueBooking';
import { venueBookingsApi } from '../api/venueBookingsApi';

const VenueBookingsList: React.FC = () => {
  const [bookings, setBookings] = useState<VenueBooking[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<VenueBookingFilters>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [searchName, setSearchName] = useState('');

  useEffect(() => {
    fetchBookings();
  }, [filters]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const response = await venueBookingsApi.getAll(filters);
      console.log('Venue Bookings API Response:', response);

      if (response.success) {
        // Handle different response structures
        const data = response.data;
        let bookingItems = [];

        console.log('Venue Bookings response data:', data);

        if (Array.isArray(data)) {
          bookingItems = data;
        } else if (data && Array.isArray(data.bookings)) {
          // Backend returns data.bookings array
          bookingItems = data.bookings;
        } else if (data && Array.isArray(data.venueBookings)) {
          // Alternative naming
          bookingItems = data.venueBookings;
        } else if (data && Array.isArray(data.items)) {
          bookingItems = data.items;
        } else if (data && Array.isArray(data.data)) {
          bookingItems = data.data;
        } else {
          bookingItems = [];
        }

        console.log('Processed booking items:', bookingItems);
        setBookings(bookingItems);

        if (data && data.pagination) {
          setTotalPages(data.pagination.totalPages);
        }
      } else {
        setBookings([]);
      }
    } catch (error) {
      console.error('Error fetching venue bookings:', error);
      toast.error('Failed to fetch venue bookings');
      setBookings([]);
    } finally {
      setLoading(false);
    }
  };

  // Delete functionality removed as per requirements

  const handleStatusUpdate = async (id: string, status: string) => {
    try {
      const response = await venueBookingsApi.update(id, { status: status as any });
      if (response.success) {
        toast.success('Booking status updated successfully');
        fetchBookings();
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
      toast.error('Failed to update booking status');
    }
  };

  const handleFilterChange = (key: keyof VenueBookingFilters, value: any) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' || value === '' ? undefined : value, 
      page: 1 
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const getStatusColor = (status: string) => {
    const statusConfig = BOOKING_STATUSES.find(s => s.value === status);
    return statusConfig?.color || 'gray';
  };

  const getPriorityColor = (priority: string) => {
    const priorityConfig = BOOKING_PRIORITIES.find(p => p.value === priority);
    return priorityConfig?.color || 'gray';
  };

  const filteredBookings = searchName && Array.isArray(bookings)
    ? bookings.filter(booking =>
        booking.fullName.toLowerCase().includes(searchName.toLowerCase()) ||
        booking.email.toLowerCase().includes(searchName.toLowerCase()) ||
        booking.venueName.toLowerCase().includes(searchName.toLowerCase()) ||
        booking.message.toLowerCase().includes(searchName.toLowerCase())
      )
    : Array.isArray(bookings) ? bookings : [];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Venue Bookings Management</h1>
        <div className="flex gap-2">
          <Link
            to="/venue-bookings/upcoming"
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Calendar size={20} />
            Upcoming Events
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="Search bookings..."
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            />
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg">
              <Search size={16} />
            </button>
          </div>

          {/* Status Filter */}
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filters.status || 'all'}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            >
              <option value="all">All Statuses</option>
              {BOOKING_STATUSES.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>



          {/* Priority Filter */}
          <div>
            <select
              value={filters.priority || 'all'}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            >
              <option value="all">All Priorities</option>
              {BOOKING_PRIORITIES.map(priority => (
                <option key={priority.value} value={priority.value}>
                  {priority.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date Filter */}
          <div>
            <input
              type="date"
              value={filters.dateFrom || ''}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
              placeholder="From date"
            />
          </div>
        </div>
      </div>

      {/* Bookings Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Venue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Message
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(filteredBookings) && filteredBookings.map((booking) => (
                <tr key={booking._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{booking.fullName}</div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Mail size={12} />
                        {booking.email}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Phone size={12} />
                        {booking.phoneNumber}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        Event Planning
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Calendar size={12} />
                        {new Date(booking.dateOfPlan).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{booking.venueName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={booking.status}
                      onChange={(e) => handleStatusUpdate(booking._id, e.target.value)}
                      className={`text-xs px-2 py-1 rounded-full border-0 bg-${getStatusColor(booking.status)}-100 text-${getStatusColor(booking.status)}-800`}
                    >
                      {BOOKING_STATUSES.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getPriorityColor(booking.priority)}-100 text-${getPriorityColor(booking.priority)}-800`}>
                      {BOOKING_PRIORITIES.find(p => p.value === booking.priority)?.label}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate" title={booking.message}>
                      {booking.message}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <Link
                        to={`/venue-bookings/${booking._id}`}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg ${
                  filters.page === page
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {(!Array.isArray(filteredBookings) || filteredBookings.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No venue bookings found</p>
        </div>
      )}
    </div>
  );
};

export default VenueBookingsList;
