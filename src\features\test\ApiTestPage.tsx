import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-toastify';

// Import all API functions
import { getAllFAQs, createFAQ, getFAQStatistics } from '@/features/faqs/api/faqApi';

import { getAllComments, createComment, getCommentStatistics } from '@/features/postComments/api/commentApi';

// Import new API functions
// Removed unused imports to fix build errors

const ApiTestPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});

  const testAPI = async (apiName: string, apiFunction: () => Promise<any>) => {
    setLoading(true);
    try {
      const result = await apiFunction();
      setResults((prev: any) => ({ ...prev, [apiName]: result }));
      
      if (result.status) {
        toast.success(`${apiName} API working!`);
      } else {
        toast.error(`${apiName} API failed: ${result.message}`);
      }
      
      console.log(`${apiName} Result:`, result);
    } catch (error) {
      console.error(`${apiName} Error:`, error);
      toast.error(`${apiName} API error: ${(error as Error).message}`);
      setResults((prev: any) => ({ ...prev, [apiName]: { error: (error as Error).message } }));
    } finally {
      setLoading(false);
    }
  };

  const testFAQsAPI = () => {
    testAPI('FAQs GetAll', () => getAllFAQs({ page: 1, limit: 5 }));
  };

  const testFAQsStats = () => {
    testAPI('FAQs Statistics', () => getFAQStatistics());
  };

  const testCreateFAQ = () => {
    testAPI('FAQs Create', () => createFAQ({
      question: 'Test FAQ Question?',
      answer: 'This is a test FAQ answer.',
      category: 'Test',
      status: 'active',
      order: 1
    }));
  };



  const testCommentsAPI = () => {
    testAPI('Comments GetAll', () => getAllComments({ page: 1, limit: 5 }));
  };

  const testCommentsStats = () => {
    testAPI('Comments Statistics', () => getCommentStatistics());
  };

  const testCreateComment = () => {
    testAPI('Comments Create', () => createComment({
      name: 'Test User',
      email: '<EMAIL>',
      comment: 'This is a test comment.',
      blogId: 1
    }));
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900">API Test Page</h1>
        <p className="text-gray-600 mt-2">Test all 4 new APIs to ensure they are working properly</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* FAQs API Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">FAQs API</CardTitle>
            <CardDescription>Test FAQ endpoints</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={testFAQsAPI} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Get All FAQs
            </Button>
            <Button 
              onClick={testFAQsStats} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test FAQ Statistics
            </Button>
            <Button 
              onClick={testCreateFAQ} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Create FAQ
            </Button>
            {results['FAQs GetAll'] && (
              <div className="text-xs p-2 bg-gray-100 rounded">
                Status: {results['FAQs GetAll'].status ? 'Success' : 'Failed'}
              </div>
            )}
          </CardContent>
        </Card>



        {/* Comments API Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Comments API</CardTitle>
            <CardDescription>Test Post Comment endpoints</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={testCommentsAPI} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Get All Comments
            </Button>
            <Button 
              onClick={testCommentsStats} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Comment Statistics
            </Button>
            <Button 
              onClick={testCreateComment} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Create Comment
            </Button>
            {results['Comments GetAll'] && (
              <div className="text-xs p-2 bg-gray-100 rounded">
                Status: {results['Comments GetAll'].status ? 'Success' : 'Failed'}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Results Display */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>Detailed API test results</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(results, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiTestPage;
