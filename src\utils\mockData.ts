// Mock data for testing when backend is not available

export const mockGalleries = [
  {
    _id: "68608672f40db0d48cc9f099",
    title: "Beautiful Wedding Gallery",
    description: "A collection of stunning wedding moments captured in time",
    keywords: ["wedding", "photography", "celebration"],
    category: "wedding" as const,
    sortOrder: 1,
    image: "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Wedding+Gallery",
    imageUrl: "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Wedding+Gallery",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f100",
    title: "Corporate Event Highlights",
    description: "Professional corporate event photography",
    keywords: ["corporate", "business", "professional"],
    category: "corporate" as const,
    sortOrder: 2,
    image: "https://via.placeholder.com/400x300/059669/FFFFFF?text=Corporate+Event",
    imageUrl: "https://via.placeholder.com/400x300/059669/FFFFFF?text=Corporate+Event",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockReviews = [
  {
    _id: "68608ab0bd4567f54be39e6a",
    name: "John Doe",
    relationship: "groom" as const,
    review: "Amazing service! The team made our wedding day absolutely perfect. Highly recommended!",
    star: 5,
    eventType: "wedding" as const,
    isFeatured: true,
    imageUrl: "https://via.placeholder.com/100x100/4F46E5/FFFFFF?text=JD",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608ab0bd4567f54be39e6b",
    name: "Sarah Smith",
    relationship: "bride" as const,
    review: "Professional and courteous staff. Everything was handled perfectly!",
    star: 5,
    eventType: "wedding" as const,
    isFeatured: false,
    imageUrl: "https://via.placeholder.com/100x100/EC4899/FFFFFF?text=SS",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockHeroSections = [
  {
    _id: "68608816f40db0d48cc9f122",
    title: "Welcome to ParvEvent",
    subtitle: "Creating Memorable Moments",
    description: "We specialize in making your special day unforgettable with our professional event management services",
    buttonText: "Get Started",
    buttonLink: "/contact",
    isPrimary: true,
    displayDuration: 5000,
    isActive: true,
    sortOrder: 1,
    image: "https://via.placeholder.com/1200x600/4F46E5/FFFFFF?text=Welcome+to+ParvEvent",
    imageUrl: "https://via.placeholder.com/1200x600/4F46E5/FFFFFF?text=Welcome+to+ParvEvent",
    images: [
      "https://via.placeholder.com/1200x600/059669/FFFFFF?text=Image+1",
      "https://via.placeholder.com/1200x600/DC2626/FFFFFF?text=Image+2",
      "https://via.placeholder.com/1200x600/7C3AED/FFFFFF?text=Image+3"
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608816f40db0d48cc9f123",
    title: "Luxury Wedding Planning",
    subtitle: "Your Dream Wedding Awaits",
    description: "From intimate ceremonies to grand celebrations, we create magical wedding experiences",
    buttonText: "Plan Your Wedding",
    buttonLink: "/services/wedding",
    isPrimary: false,
    displayDuration: 4000,
    isActive: false,
    sortOrder: 2,
    image: "https://via.placeholder.com/1200x600/EC4899/FFFFFF?text=Luxury+Wedding",
    imageUrl: "https://via.placeholder.com/1200x600/EC4899/FFFFFF?text=Luxury+Wedding",
    images: [
      "https://via.placeholder.com/1200x600/F59E0B/FFFFFF?text=Wedding+1",
      "https://via.placeholder.com/1200x600/10B981/FFFFFF?text=Wedding+2"
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockVenues = [
  {
    _id: "68608672f40db0d48cc9f101",
    name: "Grand Palace Banquet Hall",
    image: "https://via.placeholder.com/400x300/059669/FFFFFF?text=Grand+Palace",
    imageUrl: "https://via.placeholder.com/400x300/059669/FFFFFF?text=Grand+Palace",
    venueType: "banquet-hall" as const,
    location: "123 Main Street, Mumbai, Maharashtra 400001",
    capacity: 750,
    seats: 500,
    sortOrder: 1,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f102",
    name: "Sunset Resort & Gardens",
    image: "https://via.placeholder.com/400x300/DC2626/FFFFFF?text=Sunset+Resort",
    imageUrl: "https://via.placeholder.com/400x300/DC2626/FFFFFF?text=Sunset+Resort",
    venueType: "resort" as const,
    location: "456 Beach Road, Goa 403001",
    capacity: 1000,
    seats: 800,
    sortOrder: 2,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f103",
    name: "Royal Garden Palace",
    image: "https://via.placeholder.com/400x300/7C3AED/FFFFFF?text=Royal+Garden",
    imageUrl: "https://via.placeholder.com/400x300/7C3AED/FFFFFF?text=Royal+Garden",
    venueType: "palace" as const,
    location: "789 Heritage Lane, Rajasthan 302001",
    capacity: 1200,
    seats: 1000,
    sortOrder: 3,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockServices = [
  {
    _id: "68608672f40db0d48cc9f201",
    title: "Wedding Planning",
    description: "Complete wedding planning services from concept to execution. We handle every detail to make your special day perfect.",
    description2: "Our experienced team ensures seamless coordination and memorable experiences for you and your guests.",
    icons: "https://parvevent.s3.ap-southeast-2.amazonaws.com/services/icons/1751200278607-182445324.svg",
    image: "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Wedding+Planning",
    howWeDoIt: [
      {
        title: "Step 1: Initial Consultation",
        description: "We start with a detailed consultation to understand your vision, preferences, and budget. This helps us create a personalized plan for your special day."
      },
      {
        title: "Step 2: Venue & Vendor Selection",
        description: "Our team helps you select the perfect venue and coordinates with trusted vendors including caterers, decorators, photographers, and musicians."
      },
      {
        title: "Step 3: Timeline & Budget Management",
        description: "We create a detailed timeline and manage your budget effectively, ensuring everything stays on track and within your financial plan."
      },
      {
        title: "Step 4: Day-of Coordination",
        description: "On your wedding day, our team handles all coordination and execution, allowing you to relax and enjoy your special moment."
      },
      {
        title: "Step 5: Post-Event Follow-up",
        description: "After the event, we help with cleanup, vendor payments, and creating lasting memories with photo and video compilation."
      }
    ],
    sortOrder: 1,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f202",
    title: "Corporate Events",
    description: "Professional corporate event management for conferences, seminars, product launches, and team building activities.",
    description2: "Tailored solutions for businesses looking to create impactful and memorable corporate experiences.",
    icons: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='16' y='8' width='32' height='48' fill='%23059669'/%3E%3Crect x='20' y='16' width='8' height='8' fill='white'/%3E%3Crect x='36' y='16' width='8' height='8' fill='white'/%3E%3Crect x='20' y='28' width='8' height='8' fill='white'/%3E%3Crect x='36' y='28' width='8' height='8' fill='white'/%3E%3C/svg%3E",
    image: "https://via.placeholder.com/400x300/059669/FFFFFF?text=Corporate+Events",
    howWeDoIt: [
      {
        title: "Step 1: Objective Analysis",
        description: "We analyze your business objectives and set clear, measurable goals for your corporate event to ensure maximum impact and ROI."
      },
      {
        title: "Step 2: Audience Research",
        description: "Our team conducts thorough audience research to understand your attendees and create engaging experiences tailored to their interests."
      },
      {
        title: "Step 3: Venue & Technology Setup",
        description: "We handle venue selection, technology setup, and all logistical arrangements to create a professional and seamless event environment."
      },
      {
        title: "Step 4: Content & Speaker Coordination",
        description: "We coordinate with speakers, manage content flow, and ensure all presentations and activities align with your event objectives."
      }
    ],
    sortOrder: 2,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f203",
    title: "Birthday Celebrations",
    description: "Memorable birthday party planning for all ages. From intimate gatherings to grand celebrations.",
    description2: "Creative themes, entertainment, and personalized touches to make every birthday special.",
    icons: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='32' cy='32' r='24' fill='%23DC2626'/%3E%3Crect x='30' y='16' width='4' height='12' fill='white'/%3E%3Ccircle cx='32' cy='14' r='2' fill='white'/%3E%3C/svg%3E",
    image: "https://via.placeholder.com/400x300/DC2626/FFFFFF?text=Birthday+Party",
    howWeDoIt: [
      {
        title: "Step 1: Theme Selection",
        description: "We work with you to select the perfect theme and personalize every detail to match the birthday person's interests and preferences."
      },
      {
        title: "Step 2: Venue Decoration",
        description: "Our creative team transforms the venue with beautiful decorations, lighting, and setup that brings your chosen theme to life."
      },
      {
        title: "Step 3: Entertainment Planning",
        description: "We arrange age-appropriate entertainment, games, and activities to keep all guests engaged and create lasting memories."
      }
    ],
    sortOrder: 3,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f204",
    title: "Anniversary Events",
    description: "Romantic and elegant anniversary celebration planning. Honoring love stories with beautiful events.",
    description2: "From intimate dinners to grand celebrations, we create the perfect atmosphere for your milestone.",
    icons: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M32 52L16 36C12 32 12 24 16 20C20 16 28 16 32 20C36 16 44 16 48 20C52 24 52 32 48 36L32 52Z' fill='%237C3AED'/%3E%3C/svg%3E",
    image: "https://via.placeholder.com/400x300/7C3AED/FFFFFF?text=Anniversary",
    howWeDoIt: [
      {
        title: "Step 1: Milestone Planning",
        description: "We help you plan the perfect celebration that honors your journey and creates new memories for years to come."
      },
      {
        title: "Step 2: Romantic Setup",
        description: "Our team creates a romantic and elegant atmosphere with beautiful decorations, lighting, and ambiance."
      }
    ],
    sortOrder: 4,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockVenueBookings = [
  {
    _id: "68608672f40db0d48cc9f301",
    fullName: "Rajesh Kumar",
    email: "<EMAIL>",
    phoneNumber: "9876543210",
    venueId: "68608672f40db0d48cc9f101",
    venueName: "Grand Palace Banquet Hall",
    dateOfPlan: "2024-02-15",
    message: "We are planning a traditional Hindu wedding ceremony for 250 guests. We need vegetarian catering, traditional decorations, photography, music system, and parking arrangements. Please provide a detailed quote.",
    status: "new" as const,
    priority: "high" as const,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f302",
    fullName: "Priya Sharma",
    email: "<EMAIL>",
    phoneNumber: "9123456789",
    venueId: "68608672f40db0d48cc9f102",
    venueName: "Sunset Resort & Gardens",
    dateOfPlan: "2024-03-10",
    message: "Looking to book your resort for a corporate annual day event. We expect around 150 employees and need audio-visual equipment, catering for lunch, and team building activity space.",
    status: "in-progress" as const,
    priority: "medium" as const,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f303",
    fullName: "Amit Patel",
    email: "<EMAIL>",
    phoneNumber: "9987654321",
    venueId: "68608672f40db0d48cc9f103",
    venueName: "Royal Garden Palace",
    dateOfPlan: "2024-04-05",
    message: "Planning my daughter's 18th birthday party. Need a beautiful venue with good ambiance, DJ setup, catering for 80 people, and photography services. Budget is flexible for a memorable celebration.",
    status: "resolved" as const,
    priority: "low" as const,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "68608672f40db0d48cc9f304",
    fullName: "Neha Gupta",
    email: "<EMAIL>",
    phoneNumber: "9555666777",
    venueId: "68608672f40db0d48cc9f101",
    venueName: "Grand Palace Banquet Hall",
    dateOfPlan: "2024-05-20",
    message: "25th wedding anniversary celebration for 100 guests. Looking for elegant decorations, live music, special anniversary cake, and romantic ambiance. Please share package details.",
    status: "closed" as const,
    priority: "urgent" as const,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const mockReviewStats = {
  totalReviews: 25,
  averageRating: 4.8,
  ratingDistribution: {
    1: 0,
    2: 1,
    3: 2,
    4: 7,
    5: 15,
  },
  featuredCount: 8,
};
