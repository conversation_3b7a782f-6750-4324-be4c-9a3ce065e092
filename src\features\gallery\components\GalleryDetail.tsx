import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Edit, Trash2, Calendar, Tag, Hash } from 'lucide-react';
import { Gallery, GALLERY_CATEGORIES } from '../types/gallery';
import { galleryApi } from '../api/galleryApi';

const GalleryDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [gallery, setGallery] = useState<Gallery | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchGallery();
    }
  }, [id]);

  const fetchGallery = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await galleryApi.getById(id);
      if (response.success) {
        setGallery(response.data);
      } else {
        toast.error('Gallery item not found');
        navigate('/gallery');
      }
    } catch (error) {
      console.error('Error fetching gallery:', error);
      toast.error('Failed to fetch gallery data');
      navigate('/gallery');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!gallery || !window.confirm('Are you sure you want to delete this gallery item?')) {
      return;
    }

    try {
      const response = await galleryApi.delete(gallery._id);
      if (response.success) {
        toast.success('Gallery item deleted successfully');
        navigate('/gallery');
      }
    } catch (error) {
      console.error('Error deleting gallery:', error);
      toast.error('Failed to delete gallery item');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!gallery) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Gallery item not found</p>
          <Link
            to="/gallery"
            className="text-blue-500 hover:text-blue-600 mt-2 inline-block"
          >
            Back to Gallery
          </Link>
        </div>
      </div>
    );
  }

  const categoryLabel = GALLERY_CATEGORIES.find(cat => cat.value === gallery.category)?.label || gallery.category;

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/gallery')}
            className="text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Gallery Details</h1>
        </div>
        <div className="flex gap-2">
          <Link
            to={`/gallery/edit/${gallery.fixedId || gallery._id}`}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Edit size={16} />
            Edit
          </Link>
          <button
            onClick={handleDelete}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Trash2 size={16} />
            Delete
          </button>
        </div>
      </div>

      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* Image */}
        <div className="aspect-w-16 aspect-h-9">
          <img
            src={gallery.imageUrl || gallery.image}
            alt={gallery.title}
            className="w-full h-96 object-cover"
          />
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{gallery.title}</h2>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar size={16} />
                  <span>Created: {new Date(gallery.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Hash size={16} />
                  <span>Order: {gallery.sortOrder}</span>
                </div>
              </div>
            </div>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              {categoryLabel}
            </span>
          </div>

          {/* Description */}
          {gallery.description && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
              <p className="text-gray-700 leading-relaxed">{gallery.description}</p>
            </div>
          )}

          {/* Keywords */}
          {gallery.keywords.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Tag size={18} />
                Keywords
              </h3>
              <div className="flex flex-wrap gap-2">
                {gallery.keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">ID:</span>
                <span className="ml-2 text-gray-600">{gallery._id}</span>
              </div>
              {gallery.fixedId && (
                <div>
                  <span className="font-medium text-gray-700">Fixed ID:</span>
                  <span className="ml-2 text-gray-600">{gallery.fixedId}</span>
                </div>
              )}
              <div>
                <span className="font-medium text-gray-700">Created:</span>
                <span className="ml-2 text-gray-600">
                  {new Date(gallery.createdAt).toLocaleString()}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Updated:</span>
                <span className="ml-2 text-gray-600">
                  {new Date(gallery.updatedAt).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GalleryDetail;
