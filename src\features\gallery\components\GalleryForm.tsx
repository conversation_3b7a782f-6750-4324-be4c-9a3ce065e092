import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Upload, X } from 'lucide-react';
import { Gallery, CreateGalleryRequest, UpdateGalleryRequest, GALLERY_CATEGORIES } from '../types/gallery';
import { galleryApi } from '../api/galleryApi';

interface GalleryFormProps {
  mode: 'create' | 'edit';
}

const GalleryForm: React.FC<GalleryFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [gallery, setGallery] = useState<Gallery | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [keywordInput, setKeywordInput] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    keywords: [] as string[],
    category: 'wedding' as const,
    sortOrder: 1,
    image: null as File | null,
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      fetchGallery();
    }
  }, [mode, id]);

  const fetchGallery = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await galleryApi.getById(id);
      if (response.success) {
        const galleryData = response.data;
        setGallery(galleryData);
        setFormData({
          title: galleryData.title,
          description: galleryData.description,
          keywords: galleryData.keywords,
          category: galleryData.category,
          sortOrder: galleryData.sortOrder,
          image: null,
        });
        setImagePreview(galleryData.imageUrl || galleryData.image);
      }
    } catch (error) {
      console.error('Error fetching gallery:', error);
      toast.error('Failed to fetch gallery data');
      navigate('/gallery');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'sortOrder' ? parseInt(value) || 0 : value,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, keywordInput.trim()],
      }));
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    if (mode === 'create' && !formData.image) {
      toast.error('Image is required');
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        const createData: CreateGalleryRequest = {
          title: formData.title,
          description: formData.description,
          keywords: formData.keywords,
          category: formData.category,
          sortOrder: formData.sortOrder,
          image: formData.image!,
        };
        
        const response = await galleryApi.create(createData);
        if (response.success) {
          toast.success('Gallery item created successfully');
          navigate('/gallery');
        } else {
          toast.error(response.message || 'Failed to create gallery item');
          console.error('Gallery creation failed:', response);
        }
      } else if (mode === 'edit' && id) {
        const updateData: UpdateGalleryRequest = {
          title: formData.title,
          description: formData.description,
          keywords: formData.keywords,
          category: formData.category,
          sortOrder: formData.sortOrder,
        };
        
        if (formData.image) {
          updateData.image = formData.image;
        }
        
        const response = await galleryApi.update(id, updateData);
        if (response.success) {
          toast.success('Gallery item updated successfully');
          navigate('/gallery');
        } else {
          toast.error(response.message || 'Failed to update gallery item');
          console.error('Gallery update failed:', response);
        }
      }
    } catch (error) {
      console.error('Error saving gallery:', error);
      toast.error(`Failed to ${mode} gallery item`);
    } finally {
      setLoading(false);
    }
  };

  if (mode === 'edit' && loading && !gallery) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/gallery')}
          className="text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create Gallery Item' : 'Edit Gallery Item'}
        </h1>
      </div>

      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {GALLERY_CATEGORIES.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
          </div>

          {/* Sort Order */}
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
              Sort Order
            </label>
            <input
              type="number"
              id="sortOrder"
              name="sortOrder"
              value={formData.sortOrder}
              onChange={handleInputChange}
              min="1"
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Keywords */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Keywords
            </label>
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                placeholder="Add keyword..."
                className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={addKeyword}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
              >
                Add
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                >
                  {keyword}
                  <button
                    type="button"
                    onClick={() => removeKeyword(keyword)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <X size={14} />
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image {mode === 'create' && '*'}
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {imagePreview ? (
                <div className="space-y-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="max-w-full h-48 object-cover mx-auto rounded-lg"
                  />
                  <div>
                    <input
                      type="file"
                      id="image"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="image"
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                    >
                      <Upload size={16} />
                      Change Image
                    </label>
                  </div>
                </div>
              ) : (
                <div>
                  <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                  <input
                    type="file"
                    id="image"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                    required={mode === 'create'}
                  />
                  <label
                    htmlFor="image"
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                  >
                    <Upload size={16} />
                    Upload Image
                  </label>
                  <p className="text-gray-500 text-sm mt-2">
                    Click to upload an image (JPG, PNG, GIF)
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => navigate('/gallery')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg disabled:opacity-50"
            >
              {loading ? 'Saving...' : mode === 'create' ? 'Create' : 'Update'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GalleryForm;
