import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Upload, Crown, Power } from 'lucide-react';
import { HeroSection, CreateHeroSectionRequest, UpdateHeroSectionRequest } from '../types/heroSection';
import { heroSectionApi } from '../api/heroSectionApi';

interface HeroSectionFormProps {
  mode: 'create' | 'edit';
}

const HeroSectionForm: React.FC<HeroSectionFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [heroSection, setHeroSection] = useState<HeroSection | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    description: '',
    buttonText: 'Learn More',
    buttonLink: '#',
    isPrimary: false,
    displayDuration: 5000,
    isActive: true,
    sortOrder: 0,
    image: null as File | null,
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      fetchHeroSection();
    }
  }, [mode, id]);

  const fetchHeroSection = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await heroSectionApi.getById(id);
      if (response.success) {
        const heroData = response.data;
        setHeroSection(heroData);
        setFormData({
          title: heroData.title,
          subtitle: heroData.subtitle || '',
          description: heroData.description || '',
          buttonText: heroData.buttonText,
          buttonLink: heroData.buttonLink,
          isPrimary: heroData.isPrimary,
          displayDuration: heroData.displayDuration,
          isActive: heroData.isActive,
          sortOrder: heroData.sortOrder,
          image: null,
        });
        setImagePreview(heroData.image);
      }
    } catch (error) {
      console.error('Error fetching hero section:', error);
      toast.error('Failed to fetch hero section data');
      navigate('/hero-section');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : name === 'displayDuration' ? parseInt(value) || 0 
              : value,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    // Validation: Maximum 3 images
    if (files.length > 3) {
      toast.error('Maximum 3 images allowed');
      e.target.value = ''; // Clear the input
      return;
    }

    // Validation: File size (max 5MB per file)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const oversizedFiles = files.filter(file => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      toast.error('Each image must be less than 5MB');
      e.target.value = '';
      return;
    }

    // Validation: File type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const invalidFiles = files.filter(file => !allowedTypes.includes(file.type));
    if (invalidFiles.length > 0) {
      toast.error('Only JPEG, PNG, and WebP images are allowed');
      e.target.value = '';
      return;
    }

    if (files.length > 0) {
      // For hero section, we'll use the first image as primary
      setFormData(prev => ({ ...prev, image: files[0] }));

      // Create preview for the first image
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(files[0]);

      toast.success(`${files.length} image(s) selected`);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    if (formData.displayDuration < 1000 || formData.displayDuration > 30000) {
      toast.error('Display duration must be between 1 and 30 seconds');
      return;
    }

    if (mode === 'create' && !formData.image) {
      toast.error('Image is required');
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        const createData: CreateHeroSectionRequest = {
          title: formData.title.trim(),
          subtitle: formData.subtitle.trim() || undefined,
          description: formData.description.trim() || undefined,
          buttonText: formData.buttonText.trim() || undefined,
          buttonLink: formData.buttonLink.trim() || undefined,
          isPrimary: formData.isPrimary,
          displayDuration: formData.displayDuration,
          isActive: formData.isActive,
          sortOrder: formData.sortOrder,
          image: formData.image!,
        };
        
        const response = await heroSectionApi.create(createData);
        if (response.success) {
          toast.success('Hero section created successfully');
          navigate('/hero-section');
        } else {
          toast.error(response.message || 'Failed to create hero section');
        }
      } else if (mode === 'edit' && id) {
        const updateData: UpdateHeroSectionRequest = {
          title: formData.title.trim(),
          subtitle: formData.subtitle.trim() || undefined,
          description: formData.description.trim() || undefined,
          buttonText: formData.buttonText.trim() || undefined,
          buttonLink: formData.buttonLink.trim() || undefined,
          isPrimary: formData.isPrimary,
          displayDuration: formData.displayDuration,
          isActive: formData.isActive,
          sortOrder: formData.sortOrder,
        };

        if (formData.image) {
          updateData.image = formData.image;
        }

        const response = await heroSectionApi.update(id, updateData);
        if (response.success) {
          toast.success('Hero section updated successfully');
          navigate('/hero-section');
        } else {
          toast.error(response.message || 'Failed to update hero section');
        }
      }
    } catch (error) {
      console.error('Error saving hero section:', error);
      toast.error(`Failed to ${mode} hero section`);
    } finally {
      setLoading(false);
    }
  };

  if (mode === 'edit' && loading && !heroSection) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/hero-section')}
          className="text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create Hero Section' : 'Edit Hero Section'}
        </h1>
      </div>

      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Subtitle */}
          <div>
            <label htmlFor="subtitle" className="block text-sm font-medium text-gray-700 mb-2">
              Subtitle *
            </label>
            <input
              type="text"
              id="subtitle"
              name="subtitle"
              value={formData.subtitle}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Button Text */}
          <div>
            <label htmlFor="buttonText" className="block text-sm font-medium text-gray-700 mb-2">
              Button Text
            </label>
            <input
              type="text"
              id="buttonText"
              name="buttonText"
              value={formData.buttonText}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Get Started"
            />
          </div>

          {/* Button Link */}
          <div>
            <label htmlFor="buttonLink" className="block text-sm font-medium text-gray-700 mb-2">
              Button Link
            </label>
            <input
              type="text"
              id="buttonLink"
              name="buttonLink"
              value={formData.buttonLink}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., /contact"
            />
          </div>

          {/* Display Duration */}
          <div>
            <label htmlFor="displayDuration" className="block text-sm font-medium text-gray-700 mb-2">
              Display Duration (milliseconds)
            </label>
            <input
              type="number"
              id="displayDuration"
              name="displayDuration"
              value={formData.displayDuration}
              onChange={handleInputChange}
              min="1000"
              step="1000"
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-sm text-gray-500 mt-1">
              Current: {formData.displayDuration / 1000} seconds
            </p>
          </div>

          {/* Primary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPrimary"
                name="isPrimary"
                checked={formData.isPrimary}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPrimary" className="ml-2 text-sm text-gray-900 flex items-center gap-2">
                <Crown size={16} className="text-yellow-500" />
                Set as Primary
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 text-sm text-gray-900 flex items-center gap-2">
                <Power size={16} className="text-green-500" />
                Active on Homepage
              </label>
            </div>

            <div>
              <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-1">
                Sort Order
              </label>
              <input
                type="number"
                id="sortOrder"
                name="sortOrder"
                value={formData.sortOrder}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="1"
                placeholder="1"
              />
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Hero Image {mode === 'create' && '*'}
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {imagePreview ? (
                <div className="space-y-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="max-w-full h-48 object-cover mx-auto rounded-lg"
                  />
                  <div>
                    <input
                      type="file"
                      id="image"
                      accept="image/*"
                      multiple
                      onChange={handleImageChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="image"
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                    >
                      <Upload size={16} />
                      Change Image
                    </label>
                  </div>
                </div>
              ) : (
                <div>
                  <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                  <input
                    type="file"
                    id="image"
                    accept="image/*"
                    multiple
                    onChange={handleImageChange}
                    className="hidden"
                    required={mode === 'create'}
                  />
                  <label
                    htmlFor="image"
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                  >
                    <Upload size={16} />
                    Upload Image
                  </label>
                  <p className="text-gray-500 text-sm mt-2">
                    Click to upload hero images (JPG, PNG, WebP) - Maximum 3 images, 5MB each
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Removed multiple image selection - backend supports single image only */}

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => navigate('/hero-section')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg disabled:opacity-50"
            >
              {loading ? 'Saving...' : mode === 'create' ? 'Create' : 'Update'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default HeroSectionForm;
