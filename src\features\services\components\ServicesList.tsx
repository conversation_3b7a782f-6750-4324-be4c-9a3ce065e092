import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Edit, Trash2, Plus, Search, FileText } from 'lucide-react';
import { Service, ServiceFilters } from '../types/service';
import { servicesApi } from '../api/servicesApi';
import ServiceViewModal from './ServiceViewModal';
import { mockServices } from '../../../utils/mockData';
import SvgIcon from '../../../components/SvgIcon';

const ServicesList: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const [filters, setFilters] = useState<ServiceFilters>({
    page: 1,
    limit: 12,
    sortBy: 'sortOrder',
    sortOrder: 'asc',
  });

  useEffect(() => {
    fetchServices();
  }, [filters]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await servicesApi.getAll(filters);
      if (response.success) {
        const servicesData = response.data.services || [];
        console.log('Services data loaded:', servicesData);
        console.log('First service icons:', servicesData[0]?.icons);
        setServices(servicesData);
        if (response.data.pagination) {
          setCurrentPage(response.data.pagination.currentPage);
          setTotalPages(response.data.pagination.totalPages);
        }
      } else {
        console.log('API failed, using mock data');
        console.log('Mock services:', mockServices);
        console.log('First mock service icons:', mockServices[0]?.icons);
        setServices(mockServices);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching services, using mock data:', error);
      console.log('Mock services fallback:', mockServices);
      console.log('First mock service icons:', mockServices[0]?.icons);
      setServices(mockServices);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleView = (service: Service) => {
    setSelectedService(service);
    setIsViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setSelectedService(null);
    setIsViewModalOpen(false);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this service?')) {
      return;
    }

    try {
      const response = await servicesApi.delete(id);
      if (response.success) {
        toast.success('Service deleted successfully');
        fetchServices();
      } else {
        toast.error('Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  const filteredServices = searchTerm && Array.isArray(services)
    ? services.filter(service =>
        service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : Array.isArray(services) ? services : [];

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Services</h1>
          <p className="text-gray-600 mt-1">Manage your service offerings</p>
        </div>
        <Link
          to="/services/create"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus size={20} />
          Add Service
        </Link>
      </div>

      {/* Search */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            type="submit"
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors"
          >
            Search
          </button>
        </form>
      </div>

      {/* Services Grid */}
      {filteredServices.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No services found</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new service.</p>
          <div className="mt-6">
            <Link
              to="/services/create"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" />
              Add Service
            </Link>
          </div>
        </div>
      ) : (
        <>
          {/* Debug Section
          {filteredServices.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-yellow-800 mb-2">🔍 Debug Info:</h3>
              <div className="text-sm text-yellow-700">
                <p><strong>Total Services:</strong> {filteredServices.length}</p>
                <p><strong>First Service Icon URL:</strong> {filteredServices[0]?.icons || 'No icon'}</p>
                <p><strong>Icon URL Type:</strong> {typeof filteredServices[0]?.icons}</p>
                <p><strong>Is S3 URL:</strong> {filteredServices[0]?.icons?.includes('s3.ap-southeast-2.amazonaws.com') ? 'Yes' : 'No'}</p>
                <p><strong>URL Length:</strong> {filteredServices[0]?.icons?.length || 0}</p>
              </div>
              {/* Test SVG Display */}
              {/* <div className="mt-3 flex items-center gap-4">
                <span className="text-yellow-700 font-medium">Test Icon:</span>
                <SvgIcon
                  src={filteredServices[0]?.icons}
                  alt="Test Icon"
                  className="w-8 h-8 border border-yellow-300"
                  fallback={<div className="w-8 h-8 bg-red-200 rounded flex items-center justify-center text-xs">❌</div>}
                />
                <span className="text-xs text-yellow-600">↑ This should show the SVG icon</span>
              </div>
          //   </div>
          // )} */} 

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredServices.map((service) => (
            <div key={service._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2 bg-white bg-opacity-90 p-2 rounded-lg">
                  {/* Debug Info */}
                  <div className="absolute -top-8 left-0 bg-black text-white text-xs p-1 rounded z-10 max-w-xs truncate">
                    {service.icons ? `Icon: ${service.icons.substring(0, 50)}...` : 'No icon'}
                  </div>

                  <SvgIcon
                    src={service.icons}
                    alt="Service Icon"
                    className="w-6 h-6"
                    fallback={
                      <div className="w-6 h-6 bg-gray-300 rounded flex items-center justify-center text-xs">
                        📄
                      </div>
                    }
                  />
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-bold text-lg mb-2 truncate">{service.title}</h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{service.description}</p>
                
                {service.description2 && (
                  <p className="text-gray-500 text-xs mb-3 line-clamp-1">{service.description2}</p>
                )}

                {service.howWeDoIt && service.howWeDoIt.length > 0 && (
                  <div className="flex items-center gap-1 text-xs text-blue-600 mb-3">
                    <FileText size={12} />
                    <span>{service.howWeDoIt.length} step{service.howWeDoIt.length > 1 ? 's' : ''}</span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-500">
                    Order: {service.sortOrder}
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleView(service)}
                      className="text-blue-500 hover:text-blue-600"
                      title="View"
                    >
                      <Eye size={16} />
                    </button>
                    <Link
                      to={`/services/edit/${service._id}`}
                      className="text-green-500 hover:text-green-600"
                      title="Edit"
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => handleDelete(service._id)}
                      className="text-red-500 hover:text-red-600"
                      title="Delete"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        </>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  page === currentPage
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* View Modal */}
      {selectedService && (
        <ServiceViewModal
          service={selectedService}
          isOpen={isViewModalOpen}
          onClose={handleCloseViewModal}
        />
      )}
    </div>
  );
};

export default ServicesList;
