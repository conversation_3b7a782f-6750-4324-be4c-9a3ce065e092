import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Upload, Star } from 'lucide-react';
import { Review, CreateReviewRequest, UpdateReviewRequest, RELATIONSHIP_OPTIONS, EVENT_TYPE_OPTIONS } from '../types/review';
import { reviewsApi } from '../api/reviewsApi';

interface ReviewFormProps {
  mode: 'create' | 'edit';
}

const ReviewForm: React.FC<ReviewFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [review, setReview] = useState<Review | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  const [formData, setFormData] = useState({
    name: '',
    relationship: 'client' as const,
    review: '',
    star: 5,
    eventType: 'wedding' as const,
    isFeatured: false,
    image: null as File | null,
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      fetchReview();
    }
  }, [mode, id]);

  const fetchReview = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await reviewsApi.getById(id);
      if (response.success) {
        const reviewData = response.data;
        setReview(reviewData);
        setFormData({
          name: reviewData.name,
          relationship: reviewData.relationship,
          review: reviewData.review,
          star: reviewData.star,
          eventType: reviewData.eventType,
          isFeatured: reviewData.isFeatured,
          image: null,
        });
        if (reviewData.imageUrl) {
          setImagePreview(reviewData.imageUrl);
        }
      }
    } catch (error) {
      console.error('Error fetching review:', error);
      toast.error('Failed to fetch review data');
      navigate('/reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : name === 'star' ? parseInt(value) || 1 
              : value,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleStarClick = (rating: number) => {
    setFormData(prev => ({ ...prev, star: rating }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Name is required');
      return;
    }

    if (!formData.review.trim()) {
      toast.error('Review is required');
      return;
    }

    if (formData.star < 1 || formData.star > 5) {
      toast.error('Star rating must be between 1 and 5');
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        const createData: CreateReviewRequest = {
          name: formData.name,
          relationship: formData.relationship,
          review: formData.review,
          star: formData.star,
          eventType: formData.eventType,
          isFeatured: formData.isFeatured,
        };
        
        if (formData.image) {
          createData.image = formData.image;
        }
        
        const response = await reviewsApi.create(createData);
        if (response.success) {
          toast.success('Review created successfully');
          navigate('/reviews');
        }
      } else if (mode === 'edit' && id) {
        const updateData: UpdateReviewRequest = {
          name: formData.name,
          relationship: formData.relationship,
          review: formData.review,
          star: formData.star,
          eventType: formData.eventType,
          isFeatured: formData.isFeatured,
        };
        
        if (formData.image) {
          updateData.image = formData.image;
        }
        
        const response = await reviewsApi.update(id, updateData);
        if (response.success) {
          toast.success('Review updated successfully');
          navigate('/reviews');
        }
      }
    } catch (error) {
      console.error('Error saving review:', error);
      toast.error(`Failed to ${mode} review`);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number, interactive: boolean = false) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={24}
        className={`${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        } ${interactive ? 'cursor-pointer hover:text-yellow-300' : ''}`}
        onClick={interactive ? () => handleStarClick(i + 1) : undefined}
      />
    ));
  };

  if (mode === 'edit' && loading && !review) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/reviews')}
          className="text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create Review' : 'Edit Review'}
        </h1>
      </div>

      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Reviewer Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Relationship */}
          <div>
            <label htmlFor="relationship" className="block text-sm font-medium text-gray-700 mb-2">
              Relationship
            </label>
            <select
              id="relationship"
              name="relationship"
              value={formData.relationship}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {RELATIONSHIP_OPTIONS.map(rel => (
                <option key={rel.value} value={rel.value}>
                  {rel.label}
                </option>
              ))}
            </select>
          </div>

          {/* Event Type */}
          <div>
            <label htmlFor="eventType" className="block text-sm font-medium text-gray-700 mb-2">
              Event Type
            </label>
            <select
              id="eventType"
              name="eventType"
              value={formData.eventType}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {EVENT_TYPE_OPTIONS.map(event => (
                <option key={event.value} value={event.value}>
                  {event.label}
                </option>
              ))}
            </select>
          </div>

          {/* Star Rating */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Star Rating *
            </label>
            <div className="flex items-center gap-1">
              {renderStars(formData.star, true)}
              <span className="ml-2 text-sm text-gray-600">({formData.star} star{formData.star !== 1 ? 's' : ''})</span>
            </div>
          </div>

          {/* Review Text */}
          <div>
            <label htmlFor="review" className="block text-sm font-medium text-gray-700 mb-2">
              Review *
            </label>
            <textarea
              id="review"
              name="review"
              value={formData.review}
              onChange={handleInputChange}
              rows={6}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Write the review here..."
              required
            />
          </div>

          {/* Featured */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isFeatured"
              name="isFeatured"
              checked={formData.isFeatured}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
              Mark as Featured Review
            </label>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reviewer Photo (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {imagePreview ? (
                <div className="space-y-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-24 h-24 object-cover mx-auto rounded-full"
                  />
                  <div>
                    <input
                      type="file"
                      id="image"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="image"
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                    >
                      <Upload size={16} />
                      Change Photo
                    </label>
                  </div>
                </div>
              ) : (
                <div>
                  <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                  <input
                    type="file"
                    id="image"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                  <label
                    htmlFor="image"
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center gap-2"
                  >
                    <Upload size={16} />
                    Upload Photo
                  </label>
                  <p className="text-gray-500 text-sm mt-2">
                    Optional: Upload reviewer's photo (JPG, PNG, GIF)
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => navigate('/reviews')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg disabled:opacity-50"
            >
              {loading ? 'Saving...' : mode === 'create' ? 'Create' : 'Update'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReviewForm;
