import axios from 'axios';
import { heroSectionEndpoints } from '@/globalurl/baseurl';
import {
  CreateHeroSectionRequest,
  UpdateHeroSectionRequest,
  HeroSectionFilters,
  HeroSectionResponse,
  SingleHeroSectionResponse
} from '../types/heroSection';

export const heroSectionApi = {
  // Get all hero sections with optional filters
  getAll: async (filters?: HeroSectionFilters): Promise<HeroSectionResponse> => {
    try {
      const params = new URLSearchParams();
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.activeOnly !== undefined) params.append('activeOnly', filters.activeOnly.toString());

      const url = params.toString() ? `${heroSectionEndpoints.getAll}?${params}` : heroSectionEndpoints.getAll;
      const response = await axios.get(url);
      return response.data;
    } catch (error: any) {
      console.error('Hero Section API error:', error);
      return {
        success: false,
        data: { heroSections: [], pagination: { currentPage: 1, totalPages: 0, totalItems: 0, itemsPerPage: 10, hasNextPage: false, hasPrevPage: false } },
        message: error.response?.data?.message || 'Failed to fetch hero sections'
      };
    }
  },

  // Get primary hero section
  getPrimary: async (): Promise<SingleHeroSectionResponse> => {
    try {
      const response = await axios.get(heroSectionEndpoints.getPrimary);
      return response.data;
    } catch (error: any) {
      console.error('Primary hero section API error:', error);
      return {
        success: false,
        data: {} as any,
        message: error.response?.data?.message || 'Failed to fetch primary hero section'
      };
    }
  },

  // Get hero section by ID
  getById: async (id: string): Promise<SingleHeroSectionResponse> => {
    try {
      const response = await axios.get(heroSectionEndpoints.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Hero section by ID API error:', error);
      return {
        success: false,
        data: {} as any,
        message: error.response?.data?.message || 'Failed to fetch hero section'
      };
    }
  },

  // Create new hero section
  create: async (data: CreateHeroSectionRequest): Promise<SingleHeroSectionResponse> => {
    try {
      const formData = new FormData();

      formData.append('title', data.title);
      if (data.subtitle) formData.append('subtitle', data.subtitle);
      if (data.description) formData.append('description', data.description);
      if (data.buttonText) formData.append('buttonText', data.buttonText);
      if (data.buttonLink) formData.append('buttonLink', data.buttonLink);
      if (data.isPrimary !== undefined) formData.append('isPrimary', data.isPrimary.toString());
      if (data.displayDuration !== undefined) formData.append('displayDuration', data.displayDuration.toString());
      if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString());
      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      formData.append('image', data.image);

      const response = await axios.post(heroSectionEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Hero section create error:', error);
      return {
        success: false,
        data: {} as any,
        message: error.response?.data?.message || 'Failed to create hero section'
      };
    }
  },

  // Update hero section
  update: async (id: string, data: UpdateHeroSectionRequest): Promise<SingleHeroSectionResponse> => {
    try {
      const formData = new FormData();
      if (data.title) formData.append('title', data.title);
      if (data.subtitle) formData.append('subtitle', data.subtitle);
      if (data.description) formData.append('description', data.description);
      if (data.buttonText) formData.append('buttonText', data.buttonText);
      if (data.buttonLink) formData.append('buttonLink', data.buttonLink);
      if (data.isPrimary !== undefined) formData.append('isPrimary', data.isPrimary.toString());
      if (data.displayDuration !== undefined) formData.append('displayDuration', data.displayDuration.toString());
      if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString());
      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      if (data.image) formData.append('image', data.image);

      const response = await axios.put(heroSectionEndpoints.update(id), formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Hero section update error:', error);
      return {
        success: false,
        data: {} as any,
        message: error.response?.data?.message || 'Failed to update hero section'
      };
    }
  },

  // Delete hero section
  delete: async (id: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await axios.delete(heroSectionEndpoints.delete(id));
      return response.data;
    } catch (error: any) {
      console.error('Hero section delete error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to delete hero section'
      };
    }
  },
};
