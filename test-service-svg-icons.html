<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service SVG Icons Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .service-card {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .icon-container {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .icon-container svg {
            max-width: 50px;
            max-height: 50px;
        }
        .icon-container img {
            max-width: 50px;
            max-height: 50px;
            object-fit: contain;
        }
        .service-info {
            flex: 1;
        }
        .service-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .service-description {
            color: #666;
            font-size: 14px;
        }
        .icon-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-loading { background: #fff3cd; color: #856404; }
        .debug-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .test-controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Service SVG Icons Test</h1>
            <p>Testing SVG icon display from backend API</p>
        </div>

        <div class="test-controls">
            <button class="btn" onclick="testBackendConnection()">🔌 Test Backend</button>
            <button class="btn" onclick="loadServices()">🔄 Reload Services</button>
            <button class="btn" onclick="testSingleIcon()">🧪 Test SVG Icons</button>
            <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            <p>⏳ Loading services from backend...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div class="test-section">
            <h2>📊 API Response Debug</h2>
            <div id="debug-info" class="debug-info">
                <p>Click "Reload Services" to fetch data from backend...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Services with Icons</h2>
            <div id="services-list">
                <p>No services loaded yet. Click "Reload Services" to start testing.</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Single Icon Test</h2>
            <div id="single-icon-test">
                <p>Click "Test Single Icon" to test a specific SVG URL.</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8005';
        
        async function loadServices() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const debugInfo = document.getElementById('debug-info');
            const servicesList = document.getElementById('services-list');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            
            try {
                console.log('🚀 Fetching services from:', `${API_BASE_URL}/api/services`);
                
                const response = await fetch(`${API_BASE_URL}/api/services`);
                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('📦 Full API Response:', data);
                
                // Update debug info
                debugInfo.innerHTML = `
                    <h3>🔍 API Response Debug:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Success:</strong> ${data.success}</p>
                    <p><strong>Message:</strong> ${data.message || 'N/A'}</p>
                    <p><strong>Data Structure:</strong> ${JSON.stringify(Object.keys(data), null, 2)}</p>
                    <p><strong>Services Count:</strong> ${data.data?.services?.length || data.data?.length || 0}</p>
                    <p><strong>First Service Keys:</strong> ${data.data?.services?.[0] ? JSON.stringify(Object.keys(data.data.services[0]), null, 2) : 'N/A'}</p>
                    <p><strong>Raw Response:</strong></p>
                    <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // Extract services array
                let services = [];
                if (data.success && data.data) {
                    if (data.data.services) {
                        services = data.data.services;
                    } else if (Array.isArray(data.data)) {
                        services = data.data;
                    }
                }
                
                console.log('📋 Extracted services:', services);
                
                if (services.length === 0) {
                    servicesList.innerHTML = '<p>❌ No services found in API response.</p>';
                    return;
                }
                
                // Display services
                servicesList.innerHTML = services.map((service, index) => {
                    const iconUrl = service.iconImageUrl || service.icons || service.icon;
                    const iconField = service.iconImageUrl ? 'iconImageUrl' : (service.icons ? 'icons' : 'icon');
                    
                    return `
                        <div class="service-card">
                            <div class="icon-container" id="icon-${index}">
                                ${iconUrl ? `
                                    <img src="${iconUrl}" 
                                         alt="${service.title}" 
                                         onload="updateIconStatus(${index}, 'success', 'Icon loaded successfully')"
                                         onerror="updateIconStatus(${index}, 'error', 'Failed to load icon')"
                                         style="max-width: 50px; max-height: 50px;">
                                ` : '<span style="color: #999;">No Icon</span>'}
                            </div>
                            <div class="service-info">
                                <div class="service-title">${service.title}</div>
                                <div class="service-description">${service.description}</div>
                                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                                    <strong>Icon Field:</strong> ${iconField}<br>
                                    <strong>Icon URL:</strong> <a href="${iconUrl}" target="_blank" style="color: #007bff;">${iconUrl || 'N/A'}</a>
                                </div>
                            </div>
                            <div class="icon-status status-loading" id="status-${index}">
                                Loading...
                            </div>
                        </div>
                    `;
                }).join('');
                
            } catch (err) {
                console.error('❌ Error loading services:', err);
                error.style.display = 'block';
                error.innerHTML = `
                    <h3>❌ Error Loading Services</h3>
                    <p><strong>Error:</strong> ${err.message}</p>
                    <p><strong>API URL:</strong> ${API_BASE_URL}/api/services</p>
                    <p><strong>Suggestion:</strong> Make sure the backend server is running on port 8005</p>
                `;
                
                debugInfo.innerHTML = `
                    <h3>🔍 Error Debug:</h3>
                    <p><strong>Error Type:</strong> ${err.name}</p>
                    <p><strong>Error Message:</strong> ${err.message}</p>
                    <p><strong>Stack:</strong></p>
                    <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">${err.stack}</pre>
                `;
            } finally {
                loading.style.display = 'none';
            }
        }
        
        function updateIconStatus(index, status, message) {
            const statusElement = document.getElementById(`status-${index}`);
            if (statusElement) {
                statusElement.className = `icon-status status-${status}`;
                statusElement.textContent = message;
            }
            console.log(`🎯 Icon ${index} status: ${status} - ${message}`);
        }
        
        function testSingleIcon() {
            const testContainer = document.getElementById('single-icon-test');

            // Test multiple known SVG URLs
            const testUrls = [
                {
                    name: 'S3 SVG URL',
                    url: 'https://parvevent.s3.ap-southeast-2.amazonaws.com/services/icons/1751200278607-182445324.svg',
                    description: 'Testing S3 hosted SVG file'
                },
                {
                    name: 'Data URI SVG (Corporate)',
                    url: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='16' y='8' width='32' height='48' fill='%23059669'/%3E%3Crect x='20' y='16' width='8' height='8' fill='white'/%3E%3Crect x='36' y='16' width='8' height='8' fill='white'/%3E%3Crect x='20' y='28' width='8' height='8' fill='white'/%3E%3Crect x='36' y='28' width='8' height='8' fill='white'/%3E%3C/svg%3E",
                    description: 'Testing inline SVG data URI'
                },
                {
                    name: 'Data URI SVG (Birthday)',
                    url: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='32' cy='32' r='24' fill='%23DC2626'/%3E%3Crect x='30' y='16' width='4' height='12' fill='white'/%3E%3Ccircle cx='32' cy='14' r='2' fill='white'/%3E%3C/svg%3E",
                    description: 'Testing inline SVG data URI'
                },
                {
                    name: 'Data URI SVG (Heart)',
                    url: "data:image/svg+xml,%3Csvg width='64' height='64' viewBox='0 0 64 64' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M32 52L16 36C12 32 12 24 16 20C20 16 28 16 32 20C36 16 44 16 48 20C52 24 52 32 48 36L32 52Z' fill='%237C3AED'/%3E%3C/svg%3E",
                    description: 'Testing inline SVG data URI'
                }
            ];

            testContainer.innerHTML = `
                <h3>🧪 Testing Multiple SVG Types</h3>
                ${testUrls.map((test, index) => `
                    <div class="service-card" style="margin: 10px 0;">
                        <div class="icon-container">
                            <img src="${test.url}"
                                 alt="${test.name}"
                                 onload="updateTestStatus(${index}, 'success', '✅ Loaded')"
                                 onerror="updateTestStatus(${index}, 'error', '❌ Failed')"
                                 style="max-width: 50px; max-height: 50px;">
                        </div>
                        <div class="service-info">
                            <div class="service-title">${test.name}</div>
                            <div class="service-description">${test.description}</div>
                            <div style="margin-top: 5px; font-size: 11px; color: #666; word-break: break-all;">
                                <strong>URL:</strong> ${test.url.substring(0, 100)}${test.url.length > 100 ? '...' : ''}
                            </div>
                        </div>
                        <div class="icon-status status-loading" id="test-status-${index}">
                            ⏳ Loading...
                        </div>
                    </div>
                `).join('')}
                <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px;">
                    <h4>🎯 Test Results Summary:</h4>
                    <div id="test-summary">Testing in progress...</div>
                </div>
            `;
        }

        function updateTestStatus(index, status, message) {
            const statusElement = document.getElementById(`test-status-${index}`);
            if (statusElement) {
                statusElement.className = `icon-status status-${status}`;
                statusElement.textContent = message;
            }

            // Update summary
            setTimeout(() => {
                const allStatuses = document.querySelectorAll('[id^="test-status-"]');
                const results = Array.from(allStatuses).map(el => el.textContent);
                const successCount = results.filter(r => r.includes('✅')).length;
                const failCount = results.filter(r => r.includes('❌')).length;
                const loadingCount = results.filter(r => r.includes('⏳')).length;

                document.getElementById('test-summary').innerHTML = `
                    <p><strong>✅ Success:</strong> ${successCount} | <strong>❌ Failed:</strong> ${failCount} | <strong>⏳ Loading:</strong> ${loadingCount}</p>
                    ${successCount > 0 ? '<p style="color: green;">✅ SVG icons are working!</p>' : ''}
                    ${failCount > 0 ? '<p style="color: red;">❌ Some SVG icons failed to load</p>' : ''}
                `;
            }, 100);

            console.log(`🧪 Test ${index} status: ${status} - ${message}`);
        }
        
        function clearResults() {
            document.getElementById('services-list').innerHTML = '<p>Results cleared. Click "Reload Services" to test again.</p>';
            document.getElementById('debug-info').innerHTML = '<p>Debug info cleared.</p>';
            document.getElementById('single-icon-test').innerHTML = '<p>Single icon test cleared.</p>';
            document.getElementById('error').style.display = 'none';
        }
        
        // Auto-load services when page loads
        window.addEventListener('load', () => {
            console.log('🚀 Page loaded, auto-loading services...');
            loadServices();
        });
    </script>
</body>
</html>
