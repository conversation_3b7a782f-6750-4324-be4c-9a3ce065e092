export const baseUrl = import.meta.env.VITE_API_BASE_URL;
export const imageUrl = `${import.meta.env.VITE_API_BASE_URL}/uploads/`;

// Auth API endpoints
export const authEndpoints = {
  login: `${baseUrl}/api/auth/login-email`,
  profile: `${baseUrl}/api/auth/profile`,
  logout: `${baseUrl}/api/auth/logout`,
};

// Gallery API endpoints
export const galleryEndpoints = {
  getAll: `${baseUrl}/api/gallery`,
  getById: (id: string) => `${baseUrl}/api/gallery/${id}`,
  create: `${baseUrl}/api/gallery`,
  update: (id: string) => `${baseUrl}/api/gallery/${id}`,
  delete: (id: string) => `${baseUrl}/api/gallery/${id}`,
};

// Reviews API endpoints
export const reviewsEndpoints = {
  getAll: `${baseUrl}/api/reviews`,
  getStats: `${baseUrl}/api/reviews/stats`,
  getById: (id: string) => `${baseUrl}/api/reviews/${id}`,
  create: `${baseUrl}/api/reviews`,
  update: (id: string) => `${baseUrl}/api/reviews/${id}`,
  delete: (id: string) => `${baseUrl}/api/reviews/${id}`,
};

// Hero Section API endpoints
export const heroSectionEndpoints = {
  getAll: `${baseUrl}/api/hero-section`,
  getPrimary: `${baseUrl}/api/hero-section/primary`,
  getById: (id: string) => `${baseUrl}/api/hero-section/${id}`,
  create: `${baseUrl}/api/hero-section`,
  update: (id: string) => `${baseUrl}/api/hero-section/${id}`,
  delete: (id: string) => `${baseUrl}/api/hero-section/${id}`,
};

// Venues API endpoints
export const venuesEndpoints = {
  getAll: `${baseUrl}/api/venues`,
  getById: (id: string) => `${baseUrl}/api/venues/${id}`,
  create: `${baseUrl}/api/venues`,
  update: (id: string) => `${baseUrl}/api/venues/${id}`,
  delete: (id: string) => `${baseUrl}/api/venues/${id}`,
};

// Venue Bookings API endpoints
export const venueBookingsEndpoints = {
  getAll: `${baseUrl}/api/venue-bookings`,
  getUpcoming: `${baseUrl}/api/venue-bookings/upcoming`,
  getById: (id: string) => `${baseUrl}/api/venue-bookings/${id}`,
  create: `${baseUrl}/api/venue-bookings`,
  update: (id: string) => `${baseUrl}/api/venue-bookings/${id}`,
  delete: (id: string) => `${baseUrl}/api/venue-bookings/${id}`,
};

// Services API endpoints
export const servicesEndpoints = {
  getAll: `${baseUrl}/api/services`,
  getFeatured: `${baseUrl}/api/services/featured`,
  getStats: `${baseUrl}/api/services/statistics`,
  getByCategory: (category: string) => `${baseUrl}/api/services/category/${category}`,
  getById: (id: string) => `${baseUrl}/api/services/${id}`,
  create: `${baseUrl}/api/services`,
  update: (id: string) => `${baseUrl}/api/services/${id}`,
  delete: (id: string) => `${baseUrl}/api/services/${id}`,
};

// Teams API endpoints
export const teamsEndpoints = {
  getAll: `${baseUrl}/api/team`,
  getById: (id: string) => `${baseUrl}/api/team/${id}`,
  create: `${baseUrl}/api/team`,
  update: (id: string) => `${baseUrl}/api/team/${id}`,
  delete: (id: string) => `${baseUrl}/api/team/${id}`,
};

// Blogs API endpoints
export const blogsEndpoints = {
  getAll: `${baseUrl}/api/blogs`,
  getByCategory: (category: string) => `${baseUrl}/api/blogs/category/${category}`,
  getById: (id: string) => `${baseUrl}/api/blogs/${id}`,
  create: `${baseUrl}/api/blogs`,
  update: (id: string) => `${baseUrl}/api/blogs/${id}`,
  delete: (id: string) => `${baseUrl}/api/blogs/${id}`,
};

// Comments API endpoints
export const commentsEndpoints = {
  getAll: `${baseUrl}/api/comments`,
  getApproved: `${baseUrl}/api/comments/approved`,
  getByBlog: (blogId: string) => `${baseUrl}/api/comments/blog/${blogId}`,
  getCount: (blogId: string) => `${baseUrl}/api/comments/blog/${blogId}/count`,
  getStats: `${baseUrl}/api/comments/statistics`,
  getById: (id: string) => `${baseUrl}/api/comments/${id}`,
  create: `${baseUrl}/api/comments`,
  update: (id: string) => `${baseUrl}/api/comments/${id}`,
  delete: (id: string) => `${baseUrl}/api/comments/${id}`,
};

// FAQs API endpoints
export const faqsEndpoints = {
  getAll: `${baseUrl}/api/faqs`,
  getStats: `${baseUrl}/api/faqs/statistics`,
  getByCategory: (category: string) => `${baseUrl}/api/faqs/category/${category}`,
  getById: (id: string) => `${baseUrl}/api/faqs/${id}`,
  create: `${baseUrl}/api/faqs`,
  update: (id: string) => `${baseUrl}/api/faqs/${id}`,
  delete: (id: string) => `${baseUrl}/api/faqs/${id}`,
};

// Contacts API endpoints
export const contactsEndpoints = {
  getAll: `${baseUrl}/api/contacts`,
  getStats: `${baseUrl}/api/contacts/statistics`,
  getByStatus: (status: string) => `${baseUrl}/api/contacts/status/${status}`,
  getByService: (service: string) => `${baseUrl}/api/contacts/service/${service}`,
  getById: (id: string) => `${baseUrl}/api/contacts/${id}`,
  create: `${baseUrl}/api/contacts`,
  update: (id: string) => `${baseUrl}/api/contacts/${id}`,
  delete: (id: string) => `${baseUrl}/api/contacts/${id}`,
};

// Contact Form API endpoints
export const contactFormEndpoints = {
  submit: `${baseUrl}/api/contact-form/submit`,
};

// Upload API endpoints
export const uploadEndpoints = {
  single: `${baseUrl}/api/upload/single`,
  multiple: `${baseUrl}/api/upload/multiple`,
};

// Utility API endpoints
export const utilityEndpoints = {
  testCors: `${baseUrl}/api/test-cors`,
  health: `${baseUrl}/api/health`,
};
