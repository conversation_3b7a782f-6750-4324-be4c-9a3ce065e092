import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// Force disable native extensions for Rollup
process.env.ROLLUP_NATIVE = 'false';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      // Disable native extensions
      context: 'globalThis',
    },
  },
})
