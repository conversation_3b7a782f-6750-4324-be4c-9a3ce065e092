import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ServicesList from '../components/ServicesList';
import ServiceForm from '../components/ServiceForm';

const ServicesRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ServicesList />} />
      <Route path="create" element={<ServiceForm mode="create" />} />
      <Route path="edit/:id" element={<ServiceForm mode="edit" />} />
      <Route path=":id" element={<div>Service Details - Coming Soon</div>} />
    </Routes>
  );
};

export default ServicesRoutes;
