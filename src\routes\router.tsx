import { Navigate, Route, createBrowserRouter, createRoutesFromElements } from "react-router-dom";
import AdminDashboard from "@/features/dashboard/routes/dashboard-page";
import DashboardPage from "@/features/layout/dashboard-layout";
import BlogPageRoutes from "@/features/blog/routes/index";
import Form from "@/features/userform/index";
// import CareerFormPage from sd"@/features/careerform/index";

import ServicesRoutes from "@/features/services/routes";
import HomeHeroRoutes from "@/features/homeherosection";
import RateReviewRoutes from "@/features/RaviewRate";
import TeamRoutes from "@/features/teams";
import ContactRoutes from "@/features/contact/routes";
import FAQRoutes from "@/features/faqs/routes";

import PostCommentRoutes from "@/features/postComments/routes";
import ApiTestPage from "@/features/test/ApiTestPage";
import { LoginPage } from "@/features/auth/pages/LoginPage";
import { PrivateRoute, PublicRoute } from "@/components/auth";
import ErrorBoundary from "@/components/ErrorBoundary";

// New API Features
import GalleryRoutes from "@/features/gallery/routes";
import ReviewsRoutes from "@/features/reviews/routes";
import HeroSectionRoutes from "@/features/hero-section/routes";
import VenuesRoutes from "@/features/venues/routes";
import VenueBookingsRoutes from "@/features/venue-bookings/routes";

export const router = createBrowserRouter(
  createRoutesFromElements(
    <>
      {/* Public Routes - Only accessible when NOT logged in */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        }
      />

      {/* Private Routes - Only accessible when logged in */}
      <Route
        element={
          <PrivateRoute>
            <DashboardPage />
          </PrivateRoute>
        }
      >
        {/* Default Route */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard Route */}
        <Route path="/dashboard" element={<AdminDashboard />} />

        {/* New API Features */}
        <Route path="/gallery/*" element={
          <ErrorBoundary>
            <GalleryRoutes />
          </ErrorBoundary>
        } />
        <Route path="/reviews/*" element={
          <ErrorBoundary>
            <ReviewsRoutes />
          </ErrorBoundary>
        } />
        <Route path="/hero-section/*" element={
          <ErrorBoundary>
            <HeroSectionRoutes />
          </ErrorBoundary>
        } />
        <Route path="/venues/*" element={
          <ErrorBoundary>
            <VenuesRoutes />
          </ErrorBoundary>
        } />
        <Route path="/venue-bookings/*" element={
          <ErrorBoundary>
            <VenueBookingsRoutes />
          </ErrorBoundary>
        } />

        {/* Existing Routes */}
        <Route path="/form/*" element={<Form />} />
        <Route path="/blog/*" element={<BlogPageRoutes />} />

        <Route path="/teams/*" element={<TeamRoutes />} />
        <Route path="/services/*" element={<ServicesRoutes />} />
        <Route path="/herosection/*" element={<HomeHeroRoutes />} />
        <Route path="/rate-review/*" element={<RateReviewRoutes />} />
        <Route path="/contact/*" element={<ContactRoutes />} />
        <Route path="/faqs/*" element={<FAQRoutes />} />

        <Route path="/post-comments/*" element={<PostCommentRoutes />} />
        <Route path="/api-test" element={<ApiTestPage />} />
      </Route>

      {/* Catch all route - redirect to login if not authenticated, dashboard if authenticated */}
      <Route path="*" element={<Navigate to="/login" replace />} />
    </>
  )
);
