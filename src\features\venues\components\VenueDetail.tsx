import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { 
  ArrowLeft, 
  MapPin, 
  Users, 
  Calendar,
  Building,
  Image as ImageIcon,
  Info
} from 'lucide-react';
import { Venue } from '../types/venue';
import { venuesApi } from '../api/venuesApi';

const VenueDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [venue, setVenue] = useState<Venue | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchVenueDetail();
    }
  }, [id]);

  const fetchVenueDetail = async () => {
    try {
      setLoading(true);
      const response = await venuesApi.getById(id!);
      
      if (response.success) {
        setVenue(response.data);
      } else {
        toast.error('Failed to fetch venue details');
        navigate('/venues');
      }
    } catch (error) {
      console.error('Error fetching venue detail:', error);
      toast.error('Failed to fetch venue details');
      navigate('/venues');
    } finally {
      setLoading(false);
    }
  };

  const getVenueTypeLabel = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'banquet-hall': 'Banquet Hall',
      'outdoor': 'Outdoor',
      'resort': 'Resort',
      'hotel': 'Hotel',
      'farmhouse': 'Farmhouse',
      'palace': 'Palace',
      'garden': 'Garden',
      'beach': 'Beach',
      'other': 'Other'
    };
    return typeMap[type] || type;
  };

  const getVenueTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'banquet-hall': 'bg-blue-100 text-blue-800 border-blue-200',
      'outdoor': 'bg-green-100 text-green-800 border-green-200',
      'resort': 'bg-purple-100 text-purple-800 border-purple-200',
      'hotel': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'farmhouse': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'palace': 'bg-pink-100 text-pink-800 border-pink-200',
      'garden': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'beach': 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'other': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colorMap[type] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!venue) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">Venue not found</p>
        <button
          onClick={() => navigate('/venues')}
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          Back to Venues
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/venues')}
            className="text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Venue Details</h1>
        </div>
        
        <div className="flex items-center gap-3">
          <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getVenueTypeColor(venue.venueType)}`}>
            {getVenueTypeLabel(venue.venueType)}
          </div>
          <div className={`px-3 py-1 rounded-full border text-sm font-medium ${
            venue.isActive 
              ? 'bg-green-100 text-green-800 border-green-200' 
              : 'bg-red-100 text-red-800 border-red-200'
          }`}>
            {venue.isActive ? 'Active' : 'Inactive'}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Venue Image */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="relative">
              <img
                src={venue.image}
                alt={venue.name}
                className="w-full h-96 object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-venue.jpg';
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                <div className="p-6 text-white">
                  <h2 className="text-4xl font-bold mb-2">{venue.name}</h2>
                  <div className="flex items-center gap-2 text-lg">
                    <MapPin className="w-5 h-5" />
                    <span>{venue.location}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Venue Information */}
          <div className="mt-6 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Info className="w-5 h-5" />
              Venue Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Venue Name</label>
                  <p className="text-lg font-semibold text-gray-900">{venue.name}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Venue Type</label>
                  <div className={`inline-flex px-3 py-1 rounded-full border text-sm font-medium ${getVenueTypeColor(venue.venueType)}`}>
                    <Building className="w-4 h-4 mr-2" />
                    {getVenueTypeLabel(venue.venueType)}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Location</label>
                  <div className="flex items-start gap-2">
                    <MapPin className="w-5 h-5 text-gray-500 mt-0.5" />
                    <p className="text-gray-900">{venue.location}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Total Capacity</label>
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-gray-500" />
                    <p className="text-lg font-semibold text-gray-900">{venue.capacity.toLocaleString()} people</p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Seating Capacity</label>
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-gray-500" />
                    <p className="text-lg font-semibold text-gray-900">{venue.seats.toLocaleString()} seats</p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                  <div className={`inline-flex px-3 py-1 rounded-full border text-sm font-medium ${
                    venue.isActive 
                      ? 'bg-green-100 text-green-800 border-green-200' 
                      : 'bg-red-100 text-red-800 border-red-200'
                  }`}>
                    {venue.isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar Information */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Capacity</span>
                <span className="font-semibold">{venue.capacity.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Seats</span>
                <span className="font-semibold">{venue.seats.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Sort Order</span>
                <span className="font-semibold">{venue.sortOrder}</span>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Timestamps</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Created</p>
                  <p className="text-sm font-medium">{new Date(venue.createdAt).toLocaleString()}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Last Updated</p>
                  <p className="text-sm font-medium">{new Date(venue.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Image Information */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              Image Information
            </h3>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-gray-600">Image URL</p>
                <p className="text-xs text-gray-500 break-all mt-1">{venue.image}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VenueDetail;
