import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Edit, Trash2, Plus, Crown, Clock, Power, PowerOff } from 'lucide-react';
import { HeroSection, HeroSectionFilters } from '../types/heroSection';
import { heroSectionApi } from '../api/heroSectionApi';
import HeroSectionViewModal from './HeroSectionViewModal';

const HeroSectionList: React.FC = () => {
  const [heroSections, setHeroSections] = useState<HeroSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<HeroSectionFilters>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [selectedHeroSection, setSelectedHeroSection] = useState<HeroSection | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  useEffect(() => {
    fetchHeroSections();
  }, [filters]);

  const fetchHeroSections = async () => {
    try {
      setLoading(true);
      const response = await heroSectionApi.getAll(filters);
      console.log('Hero Section API Response:', response);

      if (response.success && response.data) {
        setHeroSections(response.data.heroSections || []);
        if (response.data.pagination) {
          setTotalPages(response.data.pagination.totalPages);
        }
      } else {
        console.error('Failed to fetch hero sections:', response.message);
        toast.error(response.message || 'Failed to fetch hero sections');
        setHeroSections([]);
      }
    } catch (error) {
      console.error('Error fetching hero sections:', error);
      toast.error('Failed to fetch hero sections');
      setHeroSections([]);
    } finally {
      setLoading(false);
    }
  };

  const handleView = (heroSection: HeroSection) => {
    setSelectedHeroSection(heroSection);
    setIsViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setSelectedHeroSection(null);
    setIsViewModalOpen(false);
  };

  const handleToggleActive = async (id: string, currentStatus: boolean) => {
    try {
      const response = await heroSectionApi.update(id, { isActive: !currentStatus });
      if (response.success) {
        toast.success(`Hero section ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        fetchHeroSections();
      } else {
        toast.error('Failed to update hero section status');
      }
    } catch (error) {
      console.error('Error updating hero section status:', error);
      toast.error('Failed to update hero section status');
    }
  };

  // Removed handleSetActiveImage - backend supports single image only

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this hero section?')) {
      return;
    }

    try {
      const response = await heroSectionApi.delete(id);
      if (response.success) {
        toast.success('Hero section deleted successfully');
        fetchHeroSections();
      }
    } catch (error) {
      console.error('Error deleting hero section:', error);
      toast.error('Failed to delete hero section');
    }
  };

  const handleFilterChange = (key: keyof HeroSectionFilters, value: any) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' ? undefined : value, 
      page: 1 
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Hero Section Management</h1>
        <Link
          to="/hero-section/create"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <Plus size={20} />
          Add Hero Section
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Active Filter */}
          <div className="flex items-center gap-2">
            <Power size={16} className="text-gray-500" />
            <select
              value={filters.activeOnly === undefined ? 'all' : filters.activeOnly.toString()}
              onChange={(e) => handleFilterChange('activeOnly', e.target.value === 'all' ? undefined : e.target.value === 'true')}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Sections</option>
              <option value="true">Active Only</option>
              <option value="false">Inactive Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Hero Sections Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.isArray(heroSections) && heroSections.map((heroSection) => (
          <div key={heroSection._id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="relative">
              {/* Single Image Display */}
              <img
                src={heroSection.image}
                alt={heroSection.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 left-2 flex gap-2">
                {heroSection.isPrimary && (
                  <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                    <Crown size={12} />
                    Primary
                  </div>
                )}
                <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${
                  heroSection.isActive
                    ? 'bg-green-500 text-white'
                    : 'bg-red-500 text-white'
                }`}>
                  {heroSection.isActive ? <Power size={12} /> : <PowerOff size={12} />}
                  {heroSection.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>
            </div>
            <div className="p-6">
              <h3 className="font-bold text-xl mb-2">{heroSection.title}</h3>
              <p className="text-gray-600 text-sm mb-2">{heroSection.subtitle}</p>
              <p className="text-gray-700 text-sm mb-4 line-clamp-2">{heroSection.description}</p>
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock size={16} />
                  <span>{heroSection.displayDuration / 1000}s duration</span>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(heroSection.createdAt).toLocaleDateString()}
                </div>
              </div>

              {heroSection.buttonText && (
                <div className="mb-4">
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    Button: {heroSection.buttonText}
                  </span>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <button
                    onClick={() => handleView(heroSection)}
                    className="text-blue-500 hover:text-blue-600"
                    title="View"
                  >
                    <Eye size={16} />
                  </button>
                  <Link
                    to={`/hero-section/edit/${heroSection._id}`}
                    className="text-green-500 hover:text-green-600"
                    title="Edit"
                  >
                    <Edit size={16} />
                  </Link>
                  <button
                    onClick={() => handleToggleActive(heroSection._id, heroSection.isActive)}
                    className={`${
                      heroSection.isActive
                        ? 'text-orange-500 hover:text-orange-600'
                        : 'text-green-500 hover:text-green-600'
                    }`}
                    title={heroSection.isActive ? 'Deactivate' : 'Activate'}
                  >
                    {heroSection.isActive ? <PowerOff size={16} /> : <Power size={16} />}
                  </button>
                  <button
                    onClick={() => handleDelete(heroSection._id)}
                    className="text-red-500 hover:text-red-600"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg ${
                  filters.page === page
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {(!Array.isArray(heroSections) || heroSections.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No hero sections found</p>
          <Link
            to="/hero-section/create"
            className="text-blue-500 hover:text-blue-600 mt-2 inline-block"
          >
            Create your first hero section
          </Link>
        </div>
      )}

      {/* View Modal */}
      {selectedHeroSection && (
        <HeroSectionViewModal
          heroSection={selectedHeroSection}
          isOpen={isViewModalOpen}
          onClose={handleCloseViewModal}
        />
      )}
    </div>
  );
};

export default HeroSectionList;
