// Utility functions for handling API data

/**
 * Parse keywords that might be stringified JSON
 * Backend sometimes returns keywords as ["[\"keyword1\", \"keyword2\"]"] instead of ["keyword1", "keyword2"]
 */
export const parseKeywords = (keywords: any): string[] => {
  if (!Array.isArray(keywords)) {
    return [];
  }

  // If keywords is empty, return empty array
  if (keywords.length === 0) {
    return [];
  }

  // If first element is a string that looks like JSON array, parse it
  if (typeof keywords[0] === 'string' && keywords[0].startsWith('[')) {
    try {
      return JSON.parse(keywords[0]);
    } catch (e) {
      console.warn('Failed to parse keywords JSON:', keywords[0]);
      return keywords;
    }
  }

  // Otherwise return as is
  return keywords;
};

/**
 * Parse amenities that might be stringified JSON
 */
export const parseAmenities = (amenities: any): string[] => {
  if (!Array.isArray(amenities)) {
    return [];
  }

  if (amenities.length === 0) {
    return [];
  }

  if (typeof amenities[0] === 'string' && amenities[0].startsWith('[')) {
    try {
      return JSON.parse(amenities[0]);
    } catch (e) {
      console.warn('Failed to parse amenities JSON:', amenities[0]);
      return amenities;
    }
  }

  return amenities;
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return dateString;
  }
};

/**
 * Format currency
 */
export const formatCurrency = (amount: number, currency: string = 'INR'): string => {
  if (currency === 'INR') {
    return `₹${amount.toLocaleString()}`;
  }
  return `${amount.toLocaleString()} ${currency}`;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength) + '...';
};
