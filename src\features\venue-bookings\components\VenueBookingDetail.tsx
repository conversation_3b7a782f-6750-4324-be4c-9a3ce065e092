import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  Phone, 
  Mail, 
  User, 
  MessageSquare, 
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import { VenueBooking, BOOKING_STATUSES, BOOKING_PRIORITIES } from '../types/venueBooking';
import { venueBookingsApi } from '../api/venueBookingsApi';

const VenueBookingDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<VenueBooking | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      fetchBookingDetail();
    }
  }, [id]);

  const fetchBookingDetail = async () => {
    try {
      setLoading(true);
      const response = await venueBookingsApi.getById(id!);
      
      if (response.success) {
        setBooking(response.data);
      } else {
        toast.error('Failed to fetch booking details');
        navigate('/venue-bookings');
      }
    } catch (error) {
      console.error('Error fetching booking detail:', error);
      toast.error('Failed to fetch booking details');
      navigate('/venue-bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (newStatus: string) => {
    if (!booking) return;
    
    try {
      setUpdating(true);
      const response = await venueBookingsApi.update(booking._id, { 
        status: newStatus as any 
      });
      
      if (response.success) {
        setBooking(prev => prev ? { ...prev, status: newStatus as any } : null);
        toast.success('Booking status updated successfully');
      } else {
        toast.error('Failed to update booking status');
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
      toast.error('Failed to update booking status');
    } finally {
      setUpdating(false);
    }
  };

  const handlePriorityUpdate = async (newPriority: string) => {
    if (!booking) return;
    
    try {
      setUpdating(true);
      const response = await venueBookingsApi.update(booking._id, { 
        priority: newPriority as any 
      });
      
      if (response.success) {
        setBooking(prev => prev ? { ...prev, priority: newPriority as any } : null);
        toast.success('Booking priority updated successfully');
      } else {
        toast.error('Failed to update booking priority');
      }
    } catch (error) {
      console.error('Error updating booking priority:', error);
      toast.error('Failed to update booking priority');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return <AlertCircle className="w-5 h-5" />;
      case 'in-progress': return <Pause className="w-5 h-5" />;
      case 'resolved': return <CheckCircle className="w-5 h-5" />;
      case 'closed': return <XCircle className="w-5 h-5" />;
      default: return <AlertCircle className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    const statusConfig = BOOKING_STATUSES.find(s => s.value === status);
    switch (statusConfig?.color) {
      case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'yellow': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'green': return 'bg-green-100 text-green-800 border-green-200';
      case 'gray': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    const priorityConfig = BOOKING_PRIORITIES.find(p => p.value === priority);
    switch (priorityConfig?.color) {
      case 'gray': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'orange': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'red': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">Booking not found</p>
        <button
          onClick={() => navigate('/venue-bookings')}
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          Back to Bookings
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/venue-bookings')}
            className="text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Booking Details</h1>
        </div>
        
        <div className="flex items-center gap-3">
          <div className={`px-3 py-1 rounded-full border text-sm font-medium flex items-center gap-2 ${getStatusColor(booking.status)}`}>
            {getStatusIcon(booking.status)}
            {BOOKING_STATUSES.find(s => s.value === booking.status)?.label || booking.status}
          </div>
          <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getPriorityColor(booking.priority)}`}>
            {BOOKING_PRIORITIES.find(p => p.value === booking.priority)?.label || booking.priority} Priority
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Information */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Customer Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Full Name</p>
                  <p className="font-medium">{booking.fullName}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <a href={`mailto:${booking.email}`} className="font-medium text-blue-600 hover:text-blue-800">
                    {booking.email}
                  </a>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Phone Number</p>
                  <a href={`tel:${booking.phoneNumber}`} className="font-medium text-blue-600 hover:text-blue-800">
                    {booking.phoneNumber}
                  </a>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Event Date</p>
                  <p className="font-medium">{new Date(booking.dateOfPlan).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Venue Information */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Venue Information</h2>
            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-600">Venue</p>
                <p className="font-medium text-lg">{booking.venueName}</p>
              </div>
            </div>
          </div>

          {/* Message */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Customer Message
            </h2>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700 whitespace-pre-wrap">{booking.message}</p>
            </div>
          </div>
        </div>

        {/* Status Management */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Status Management</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Booking Status
                </label>
                <select
                  value={booking.status}
                  onChange={(e) => handleStatusUpdate(e.target.value)}
                  disabled={updating}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {BOOKING_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority Level
                </label>
                <select
                  value={booking.priority}
                  onChange={(e) => handlePriorityUpdate(e.target.value)}
                  disabled={updating}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {BOOKING_PRIORITIES.map(priority => (
                    <option key={priority.value} value={priority.value}>
                      {priority.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Timestamps</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Clock className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Created</p>
                  <p className="text-sm font-medium">{new Date(booking.createdAt).toLocaleString()}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Clock className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Last Updated</p>
                  <p className="text-sm font-medium">{new Date(booking.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VenueBookingDetail;
