import axios from 'axios';
import { venueBookingsEndpoints } from '@/globalurl/baseurl';
import { CreateVenueBookingRequest, UpdateVenueBookingRequest, VenueBookingFilters } from '../types/venueBooking';

export const venueBookingsApi = {
  // Get all venue bookings with optional filters
  getAll: async (filters?: VenueBookingFilters) => {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.priority) params.append('priority', filters.priority);
    if (filters?.venueId) params.append('venueId', filters.venueId);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.sortBy) params.append('sortBy', filters.sortBy);
    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

    const url = params.toString() ? `${venueBookingsEndpoints.getAll}?${params}` : venueBookingsEndpoints.getAll;
    const response = await axios.get(url);
    return response.data;
  },

  // Get upcoming venue bookings
  getUpcoming: async () => {
    const response = await axios.get(venueBookingsEndpoints.getUpcoming);
    return response.data;
  },

  // Get venue booking by ID or fixedId
  getById: async (id: string) => {
    const response = await axios.get(venueBookingsEndpoints.getById(id));
    return response.data;
  },

  // Create new venue booking (public endpoint)
  create: async (data: CreateVenueBookingRequest) => {
    try {
      const requestData = data;

      const response = await axios.post(venueBookingsEndpoints.create, requestData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Venue booking create error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create venue booking',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update venue booking (admin only)
  update: async (id: string, data: UpdateVenueBookingRequest) => {
    const response = await axios.put(venueBookingsEndpoints.update(id), data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  },

  // Delete venue booking
  delete: async (id: string) => {
    const response = await axios.delete(venueBookingsEndpoints.delete(id));
    return response.data;
  },
};
