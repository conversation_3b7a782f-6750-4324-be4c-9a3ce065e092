import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Edit, Trash2, Plus, Search, Filter } from 'lucide-react';
import { Gallery, GalleryFilters, GALLERY_CATEGORIES } from '../types/gallery';
import { galleryApi } from '../api/galleryApi';
import { parseKeywords } from '@/utils/dataHelpers';

const GalleryList: React.FC = () => {
  const [galleries, setGalleries] = useState<Gallery[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<GalleryFilters>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState('');

  useEffect(() => {
    fetchGalleries();
  }, [filters]);

  const fetchGalleries = async () => {
    try {
      setLoading(true);
      const response = await galleryApi.getAll(filters);
      console.log('Gallery API Response:', response);

      if (response.success) {
        // Handle different response structures
        const data = response.data;
        let galleryItems = [];

        console.log('Gallery response data:', data);

        if (Array.isArray(data)) {
          galleryItems = data;
        } else if (data && Array.isArray(data.galleries)) {
          // Backend returns data.galleries array
          galleryItems = data.galleries;
        } else if (data && Array.isArray(data.items)) {
          galleryItems = data.items;
        } else if (data && Array.isArray(data.data)) {
          galleryItems = data.data;
        } else {
          galleryItems = [];
        }

        console.log('Processed gallery items:', galleryItems);
        setGalleries(galleryItems);

        if (data && data.pagination) {
          setTotalPages(data.pagination.totalPages);
        }
      } else {
        setGalleries([]);
      }
    } catch (error) {
      console.error('Error fetching galleries:', error);
      toast.error('Failed to fetch galleries');
      setGalleries([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this gallery item?')) {
      return;
    }

    try {
      const response = await galleryApi.delete(id);
      if (response.success) {
        toast.success('Gallery item deleted successfully');
        fetchGalleries();
      }
    } catch (error) {
      console.error('Error deleting gallery:', error);
      toast.error('Failed to delete gallery item');
    }
  };

  const handleSearch = () => {
    setFilters(prev => ({ ...prev, keyword: searchKeyword, page: 1 }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters(prev => ({ 
      ...prev, 
      category: category === 'all' ? undefined : category, 
      page: 1 
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Gallery Management</h1>
        <Link
          to="/gallery/create"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <Plus size={20} />
          Add Gallery Item
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Search */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="Search by keyword..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-64"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg"
            >
              <Search size={16} />
            </button>
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filters.category || 'all'}
              onChange={(e) => handleCategoryFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Categories</option>
              {GALLERY_CATEGORIES.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Gallery Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.isArray(galleries) && galleries.map((gallery) => (
          <div key={gallery._id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="aspect-w-16 aspect-h-9">
              <img
                src={gallery.imageUrl || gallery.image}
                alt={gallery.title}
                className="w-full h-48 object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-lg mb-2 truncate">{gallery.title}</h3>
              <p className="text-gray-600 text-sm mb-2 line-clamp-2">{gallery.description}</p>
              <div className="flex items-center justify-between mb-3">
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {GALLERY_CATEGORIES.find(cat => cat.value === gallery.category)?.label}
                </span>
                <span className="text-xs text-gray-500">Order: {gallery.sortOrder}</span>
              </div>
              <div className="flex flex-wrap gap-1 mb-3">
                {(() => {
                  const keywords = parseKeywords(gallery.keywords);
                  return keywords.slice(0, 3).map((keyword, index) => (
                    <span key={index} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                      {keyword}
                    </span>
                  ));
                })()}
                {(() => {
                  const keywords = parseKeywords(gallery.keywords);
                  return keywords.length > 3 && (
                    <span className="text-xs text-gray-500">+{keywords.length - 3} more</span>
                  );
                })()}
              </div>
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Link
                    to={`/gallery/${gallery._id}`}
                    className="text-blue-500 hover:text-blue-600"
                    title="View"
                  >
                    <Eye size={16} />
                  </Link>
                  <Link
                    to={`/gallery/edit/${gallery._id}`}
                    className="text-green-500 hover:text-green-600"
                    title="Edit"
                  >
                    <Edit size={16} />
                  </Link>
                  <button
                    onClick={() => handleDelete(gallery._id)}
                    className="text-red-500 hover:text-red-600"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(gallery.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg ${
                  filters.page === page
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {(!Array.isArray(galleries) || galleries.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No gallery items found</p>
          <Link
            to="/gallery/create"
            className="text-blue-500 hover:text-blue-600 mt-2 inline-block"
          >
            Create your first gallery item
          </Link>
        </div>
      )}
    </div>
  );
};

export default GalleryList;
