import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Edit, Trash2, Plus, Search, Filter, Star, TrendingUp } from 'lucide-react';
import { Review, ReviewFilters, ReviewStats, RELATIONSHIP_OPTIONS, EVENT_TYPE_OPTIONS } from '../types/review';
import { reviewsApi } from '../api/reviewsApi';
import ReviewViewModal from './ReviewViewModal';

const ReviewsList: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<ReviewFilters>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [searchName, setSearchName] = useState('');
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  useEffect(() => {
    fetchReviews();
    fetchStats();
  }, [filters]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const response = await reviewsApi.getAll(filters);
      console.log('Reviews API Response:', response);

      if (response.success) {
        // Handle different response structures
        const data = response.data;
        let reviewItems = [];

        console.log('Reviews response data:', data);

        if (Array.isArray(data)) {
          reviewItems = data;
        } else if (data && Array.isArray(data.reviews)) {
          // Backend returns data.reviews array
          reviewItems = data.reviews;
        } else if (data && Array.isArray(data.items)) {
          reviewItems = data.items;
        } else if (data && Array.isArray(data.data)) {
          reviewItems = data.data;
        } else {
          reviewItems = [];
        }

        console.log('Processed review items:', reviewItems);
        setReviews(reviewItems);

        if (data && data.pagination) {
          setTotalPages(data.pagination.totalPages);
        }
      } else {
        setReviews([]);
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to fetch reviews');
      setReviews([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await reviewsApi.getStats();
      console.log('Stats API Response:', response);
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Set default stats if API fails
      setStats({
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        featuredCount: 0
      });
    }
  };

  const handleView = (review: Review) => {
    setSelectedReview(review);
    setIsViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setSelectedReview(null);
    setIsViewModalOpen(false);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this review?')) {
      return;
    }

    try {
      const response = await reviewsApi.delete(id);
      if (response.success) {
        toast.success('Review deleted successfully');
        fetchReviews();
        fetchStats();
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };

  const handleSearch = () => {
    // For name search, we'll filter on the frontend since the API doesn't have name search
    // In a real implementation, you'd add name search to the API
    fetchReviews();
  };

  const handleFilterChange = (key: keyof ReviewFilters, value: any) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' ? undefined : value, 
      page: 1 
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  const filteredReviews = searchName && Array.isArray(reviews)
    ? reviews.filter(review =>
        review.name.toLowerCase().includes(searchName.toLowerCase())
      )
    : Array.isArray(reviews) ? reviews : [];

  if (loading && !stats) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Reviews Management</h1>
        <Link
          to="/reviews/create"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <Plus size={20} />
          Add Review
        </Link>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalReviews}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Rating</p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageRating.toFixed(1)}</p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Featured Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.featuredCount}</p>
              </div>
              <Star className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">5-Star Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.ratingDistribution[5]}</p>
              </div>
              <div className="flex">
                {renderStars(5)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Search */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="Search by name..."
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-64"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg"
            >
              <Search size={16} />
            </button>
          </div>

          {/* Relationship Filter */}
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filters.relationship || 'all'}
              onChange={(e) => handleFilterChange('relationship', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Relationships</option>
              {RELATIONSHIP_OPTIONS.map(rel => (
                <option key={rel.value} value={rel.value}>
                  {rel.label}
                </option>
              ))}
            </select>
          </div>

          {/* Event Type Filter */}
          <div className="flex items-center gap-2">
            <select
              value={filters.eventType || 'all'}
              onChange={(e) => handleFilterChange('eventType', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Event Types</option>
              {EVENT_TYPE_OPTIONS.map(event => (
                <option key={event.value} value={event.value}>
                  {event.label}
                </option>
              ))}
            </select>
          </div>

          {/* Star Rating Filter */}
          <div className="flex items-center gap-2">
            <select
              value={filters.star || 'all'}
              onChange={(e) => handleFilterChange('star', e.target.value === 'all' ? undefined : parseInt(e.target.value))}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          {/* Featured Filter */}
          <div className="flex items-center gap-2">
            <select
              value={filters.isFeatured === undefined ? 'all' : filters.isFeatured.toString()}
              onChange={(e) => handleFilterChange('isFeatured', e.target.value === 'all' ? undefined : e.target.value === 'true')}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Reviews</option>
              <option value="true">Featured Only</option>
              <option value="false">Non-Featured</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Review
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Featured
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(filteredReviews) && filteredReviews.map((review) => (
                <tr key={review._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {review.imageUrl && (
                        <img
                          className="h-10 w-10 rounded-full mr-3"
                          src={review.imageUrl}
                          alt={review.name}
                        />
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">{review.name}</div>
                        <div className="text-sm text-gray-500">
                          {RELATIONSHIP_OPTIONS.find(r => r.value === review.relationship)?.label}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {renderStars(review.star)}
                      <span className="ml-2 text-sm text-gray-600">({review.star})</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {review.review}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {EVENT_TYPE_OPTIONS.find(e => e.value === review.eventType)?.label}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {review.isFeatured ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Featured
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Regular
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(review.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleView(review)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View"
                      >
                        <Eye size={16} />
                      </button>
                      <Link
                        to={`/reviews/edit/${review._id}`}
                        className="text-green-600 hover:text-green-900"
                        title="Edit"
                      >
                        <Edit size={16} />
                      </Link>
                      <button
                        onClick={() => handleDelete(review._id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg ${
                  filters.page === page
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {(!Array.isArray(filteredReviews) || filteredReviews.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No reviews found</p>
          <Link
            to="/reviews/create"
            className="text-blue-500 hover:text-blue-600 mt-2 inline-block"
          >
            Create your first review
          </Link>
        </div>
      )}

      {/* View Modal */}
      {selectedReview && (
        <ReviewViewModal
          review={selectedReview}
          isOpen={isViewModalOpen}
          onClose={handleCloseViewModal}
        />
      )}
    </div>
  );
};

export default ReviewsList;
